{"permissions": {"allow": ["Bash(grep:*)", "Bash(for file in /root/onlymonster-automations/utils/*.py)", "Bash(do)", "<PERSON><PERSON>(sed:*)", "Bash(done)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(pip install:*)", "Bash(find:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(source:*)", "WebFetch(domain:api.postpone.app)", "WebFetch(domain:cheeksglobal.slack.com)", "<PERSON><PERSON>(sudo docker ps:*)", "<PERSON><PERSON>(chmod:*)", "Ba<PERSON>(dig:*)", "<PERSON><PERSON>(nslookup:*)", "<PERSON><PERSON>(curl:*)", "Bash(./setup-docker.sh:*)", "<PERSON><PERSON>(docker compose:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(docker inspect:*)", "<PERSON><PERSON>(docker ps:*)", "Bash(cd:*)", "Bash(PYTHONPATH=/root/onlymonster-automations python store_social_data.py)", "Bash(docker port:*)", "Bash(sudo systemctl status:*)", "<PERSON><PERSON>(timeout:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(echo $HOME)", "Read(//root/**)", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(echo:*)", "Bash(export ANTHROPIC_BASE_URL=https://api.deepseek.com/anthropic)", "Bash(export API_TIMEOUT_MS=600000)", "Bash(export ANTHROPIC_MODEL=deepseek-chat)", "Bash(export ANTHROPIC_SMALL_FAST_MODEL=deepseek-chat)", "Bash(__NEW_LINE__ echo \"Environment variables set for current session:\")", "Bash(./view-tasks.sh:*)", "<PERSON><PERSON>(netstat:*)", "Bash(HOST_DATA_DIR=/root/onlymonster-automations ./deploy_dashboard.sh)", "Bash(google-chrome:*)", "Bash(chromedriver:*)", "Bash(apt-get update:*)", "Bash(apt-get install:*)", "<PERSON><PERSON>(pkill:*)", "Bash(ln:*)", "Bash(/usr/lib/chromium-browser/chromedriver:*)", "Read(/usr/bin/**)"], "deny": [], "additionalDirectories": ["/root"]}}