# OnlyMonster Automation .gitignore

# Virtual Environment
venv/
env/
ENV/
.venv/
.env/
venv*/
env*/
.virtualenv/
virtualenv/

# Python bytecode
*.pyc

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Database files
*.db
*.sqlite
*.sqlite3
onlymonster_data.db

# Environment variables and secrets
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
config_local.py

# Logs
*.log
logs/
daily_scheduler.log
scraper.log

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Selenium and Browser files
*.html
page_source.html
login_debug.html
chromedriver*
geckodriver*
*.crx
*.exe
driver/
drivers/
selenium-server-*.jar
.selenium-profile/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Flask
instance/
.webassets-cache

# Coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# celery beat schedule file
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# Backup files
*.bak
*.backup
*.old

# Test files (keep test scripts but ignore test data)
test_data/
test_output/

# Screenshots and debug files
screenshots/
debug_*.png
debug_*.jpg
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.tiff

# Service files (these might contain sensitive info)
*.service
*.timer

# Credentials and API keys (extra safety)
*credentials*
*api_key*
*token*
*secret*

# Local configuration overrides
config_override.py
local_settings.py

# Node.js (if you ever add JS components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Additional Python tools
.tox/
.coverage
.coverage.*
.cache
.pytest_cache/
.mypy_cache/
.dmypy.json
dmypy.json

# Additional temporary and cache files
*.pid
*.seed
*.pid.lock
.sass-cache/
.connect.lock
.typings/
downloaded_files/
debug/