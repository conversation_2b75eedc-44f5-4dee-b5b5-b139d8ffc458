# OnlyMonster Dashboard Tasks

## How to Use This Todo System

1. **Add Tasks**: List your tasks below in the "Pending Tasks" section
2. **Complete Tasks**: <PERSON> "complete the tasks" and it will:
   - Work through each task systematically 
   - Mark tasks as completed when done
   - Automatically refresh the webapp at cheeksdash.com after each completion
   - Show you the live updated version before moving to the next task

---

## Pending Tasks

### Analytics Features
- [ ] Create trending analysis for fan growth over time
- [ ] Add performance alerts when conversion rates drop
- [ ] Implement weekly/monthly view options for metrics

### Data Management
- [ ] Add data backup automation script

---

## Completed Tasks ✅

### Dashboard Improvements - August 30, 2025
- [x] Make it so that the onlymonster link performance and revenue metrics / onlymonster overview sections have an option for me to select a date to analyze. The date selector should only allow me to select dates that have scraped data for me to view. Refresh the webapp when finished so it shows up live.
- [x] Fix the "earnings" column on the onlymonster links performance section. Right now it's showing very inflated numbers and is probably not creating a "per day" calculation of the earnings and just looking at how much was earned since the last scrape. Refresh the webapp when finished so it shows up live.
- [x] Make it so that the star priority marker acts as a "pin" and pins those links to the top. The "pinned" ones should be saved if I were to close the page and reload it. Refresh the webapp when finished so it shows up live.
- [x] Fix the dates shown in the social media activity section. Right now it's showing that there is "invalid dates" in the GUI for the post dates. Refresh the webapp when finished so it shows up live.
- [x] The reddit username that I'm monitoring shows u/u/babycheeksx but it should just be u/babycheeksx. Refresh the webapp when finished so it shows up live.
- [x] give an indication in the GUI if one of the scrapers fails, like if my limit is reached for a scraping API or give an indication if the username does not exist (which is the case for the @fox1naomi account right now for reference). Refresh the webapp when finished so it shows up live.
- [x] put an indication in the GUI for the last time an account was posted on if there wasn't a post made in the last 24 hours. Refresh the webapp when finished so it shows up live.
- [x] make it so the website is not crawled by google
- [x] Reorganize the "revenue metrics" section on the dashboard gui. There's too much white space right now.
- [x] for the refresh all button, make it so it triggers a rescraping of all the metrics instead of just reloading the page.

---

## Task Format Examples

You can add tasks in any of these formats:

**Simple Task:**
- [ ] Fix the header alignment issue

**Detailed Task:**
- [ ] **Mobile Optimization**: Make the dashboard responsive on tablets
  - Ensure tables scroll horizontally on small screens
  - Optimize touch targets for mobile users

**Feature Request:**
- [ ] **New Feature - Data Export**: Add CSV export for OnlyMonster metrics
  - Include all table columns in export
  - Add date range selection for export
  - Show download progress indicator

**Bug Fix:**
- [ ] **Bug**: Fix calculation error in fan growth averaging
  - Issue: Shows wrong numbers when scraper misses days
  - Expected: Should average across actual time gap

---

## Notes

- Tasks will be completed in the order listed
- Each completed task triggers an automatic webapp refresh
- Complex tasks may be broken down into smaller subtasks automatically
- Priority tasks (marked with ⭐) will be handled first

**Last Updated:** August 30, 2025