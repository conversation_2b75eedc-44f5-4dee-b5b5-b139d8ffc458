#!/bin/bash
# Quick command to add a new task

if [ $# -eq 0 ]; then
    echo "Usage: ./add-task.sh \"Your task description\""
    echo "Example: ./add-task.sh \"Fix mobile responsiveness on dashboard\""
    exit 1
fi

TASK="$1"
TASKS_FILE="/root/onlymonster-automations/TASKS.md"

# Add task to the pending section
sed -i "/^### Dashboard Improvements/a - [ ] $TASK" "$TASKS_FILE"

echo "✅ Added task: $TASK"
echo "📄 View all tasks: ./view-tasks.sh"
echo "🚀 Complete tasks: Tell <PERSON> 'complete the tasks'"