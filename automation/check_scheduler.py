#!/usr/bin/env python3
"""Check the status of the social media scheduler."""

import schedule
import subprocess
import sys
from datetime import datetime

def check_scheduler_status():
    """Check if the scheduler service is running and show next run time."""
    print("Social Media Scheduler Status Check")
    print("=" * 40)
    
    # Check current time
    current_time = datetime.now()
    print(f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    
    # Check systemd service status
    try:
        result = subprocess.run(
            ['systemctl', 'is-active', 'social-media-scheduler.service'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            status = result.stdout.strip()
            print(f"Service status: {status}")
        else:
            print("Service status: inactive or not found")
            
    except Exception as e:
        print(f"Error checking service status: {e}")
    
    # Show scheduled time
    print(f"Scheduled run time: Daily at 6:00 AM Central Time (checks previous day's posts)")

    # Calculate next run
    if current_time.hour < 6:
        # Today at 6 AM
        next_run = current_time.replace(hour=6, minute=0, second=0, microsecond=0)
        print(f"Next run: Today at {next_run.strftime('%H:%M:%S')}")
    else:
        # Tomorrow at 6 AM
        from datetime import timedelta
        next_run = (current_time + timedelta(days=1)).replace(hour=6, minute=0, second=0, microsecond=0)
        print(f"Next run: Tomorrow at {next_run.strftime('%H:%M:%S')}")
    
    print("\nUseful commands:")
    print("  Check service status: sudo systemctl status social-media-scheduler.service")
    print("  View logs: sudo journalctl -u social-media-scheduler.service -f")
    print("  Stop service: sudo systemctl stop social-media-scheduler.service")
    print("  Start service: sudo systemctl start social-media-scheduler.service")
    print("  Restart service: sudo systemctl restart social-media-scheduler.service")

if __name__ == "__main__":
    check_scheduler_status()
