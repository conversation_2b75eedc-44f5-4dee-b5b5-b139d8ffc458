#!/usr/bin/env python3
"""Daily scheduler for social media monitoring using the schedule library."""

import schedule
import time
import logging
import sys
import os
from datetime import datetime
import subprocess
import signal

# Set up logging for the scheduler
REPO_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
LOG_PATH = os.path.join(REPO_ROOT, 'daily_scheduler.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_PATH),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class SocialMediaScheduler:
    """Scheduler for daily social media monitoring."""
    
    def __init__(self):
        self.running = True
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}. Shutting down gracefully...")
        self.running = False
    
    def run_social_media_check(self):
        """Run the multi-platform social media check."""
        try:
            logger.info("Starting scheduled social media check...")
            
            # Change to the repository root
            os.chdir(REPO_ROOT)

            # Run the optimized social storage (computes 24-hour window internally)
            script_path = os.path.join(REPO_ROOT, 'store_social_data_optimized.py')
            result = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True,
                timeout=600  # allow up to 10 minutes in case of slow APIs
            )
            
            if result.returncode == 0:
                logger.info("Social media check completed successfully")
                logger.info(f"Output: {result.stdout}")
            else:
                logger.error(f"Social media check failed with return code {result.returncode}")
                logger.error(f"Error output: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error("Social media check timed out after 5 minutes")
        except Exception as e:
            logger.error(f"Error running social media check: {e}")
    
    def start_scheduler(self):
        """Start the scheduler with daily job at 10pm Central Time."""
        logger.info("Starting Social Media Scheduler")
        logger.info("Scheduled to run daily at 6:00 AM Central Time to check previous 24-hour window")

        # Schedule the job for 6:00 AM Central Time to check 24-hour window (6 AM yesterday to 6 AM today)
        # Server timezone is now set to America/Chicago (Central Time)
        schedule.every().day.at("06:00").do(self.run_social_media_check)  # 6:00 AM Central Time

        logger.info("Scheduler started. Next run scheduled for 6:00 AM Central Time")
        
        # Run once immediately for testing (optional - remove if you don't want this)
        logger.info("Running initial check...")
        self.run_social_media_check()
        
        # Main scheduler loop
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except KeyboardInterrupt:
                logger.info("Received keyboard interrupt. Shutting down...")
                break
            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}")
                time.sleep(60)  # Wait a minute before retrying
        
        logger.info("Scheduler stopped")

def main():
    """Main function to start the scheduler."""
    try:
        scheduler = SocialMediaScheduler()
        scheduler.start_scheduler()
    except Exception as e:
        logger.error(f"Failed to start scheduler: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
