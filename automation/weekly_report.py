#!/usr/bin/env python3
"""
OnlyMonster Weekly Report - Comprehensive Weekly Analytics
Shows weekly subscriber growth, revenue performance, and AI insights
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlite3
import json
from datetime import datetime, timedelta
from statistics import mean, stdev
from core.analytics import TrackingAnalytics
from slack.slack_webhook import SlackWebhookNotifier
from core.link_combiner import Link<PERSON>ombiner
from core.config import DATABASE_PATH, PRIORITY_LINKS, SLACK_BOT_TOKEN, SLACK_CHANNEL_ID


def get_weekly_date_range():
    """Get the date range for the past week"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    return start_date.strftime('%m/%d/%y'), end_date.strftime('%m/%d/%y'), 7


def get_weekly_historical_data():
    """Get historical data for the past week, organized by tracking link"""
    with sqlite3.connect(DATABASE_PATH) as conn:
        cursor = conn.cursor()
        
        # Get data from the past week
        week_ago = datetime.now() - timedelta(days=7)
        
        cursor.execute('''
            SELECT tracking_link_name, clicks, fans, earnings, timestamp
            FROM tracking_data
            WHERE timestamp >= ?
            ORDER BY tracking_link_name, timestamp DESC
        ''', (week_ago,))
        
        raw_data = cursor.fetchall()
    
    # Organize data by link
    data_by_link = {}
    for name, clicks, fans, earnings, timestamp in raw_data:
        if name not in data_by_link:
            data_by_link[name] = []
        
        data_by_link[name].append({
            'clicks': clicks,
            'fans': fans,
            'earnings': earnings or 0.0,
            'timestamp': timestamp
        })
    
    # Apply link combinations
    link_combiner = LinkCombiner()
    combined_data = link_combiner.combine_historical_data(data_by_link)
    
    return combined_data


def calculate_weekly_metrics(data_by_link):
    """Calculate weekly growth metrics and performance indicators"""
    metrics = {}
    
    for link, entries in data_by_link.items():
        if len(entries) < 2:
            continue
            
        # Sort by timestamp (newest first)
        entries = sorted(entries, key=lambda x: x['timestamp'], reverse=True)
        
        # Current (most recent) vs week start (oldest in our 7-day window)
        current = entries[0]
        week_start = entries[-1]
        
        # Calculate weekly growth
        click_growth = current['clicks'] - week_start['clicks']
        fan_growth = current['fans'] - week_start['fans']
        earnings_growth = current['earnings'] - week_start['earnings']
        
        # Calculate weekly averages
        total_entries = len(entries)
        avg_weekly_clicks = sum(entry['clicks'] for entry in entries) / total_entries
        avg_weekly_fans = sum(entry['fans'] for entry in entries) / total_entries
        avg_weekly_earnings = sum(entry['earnings'] for entry in entries) / total_entries
        
        # Calculate conversion rate
        conversion_rate = (current['fans'] / current['clicks'] * 100) if current['clicks'] > 0 else 0
        
        # Calculate earnings per fan
        earnings_per_fan = current['earnings'] / current['fans'] if current['fans'] > 0 else 0
        
        metrics[link] = {
            'current_clicks': current['clicks'],
            'current_fans': current['fans'],
            'current_earnings': current['earnings'],
            'click_growth': click_growth,
            'fan_growth': fan_growth,
            'earnings_growth': earnings_growth,
            'avg_weekly_clicks': avg_weekly_clicks,
            'avg_weekly_fans': avg_weekly_fans,
            'avg_weekly_earnings': avg_weekly_earnings,
            'conversion_rate': conversion_rate,
            'earnings_per_fan': earnings_per_fan,
            'total_data_points': total_entries
        }
    
    return metrics


def generate_weekly_report():
    """Generate comprehensive weekly report"""
    start_date, end_date, days_count = get_weekly_date_range()
    
    print("📊 ONLYMONSTER WEEKLY REPORT")
    print(f"📅 Period: {start_date} → {end_date} ({days_count} days)")
    print("="*60)
    
    # Get data and calculate metrics
    data_by_link = get_weekly_historical_data()
    metrics = calculate_weekly_metrics(data_by_link)
    
    if not metrics:
        print("❌ No sufficient data available for weekly analysis")
        return None
    
    # 1. WEEKLY SUBSCRIBER GROWTH
    print("\n👥 WEEKLY SUBSCRIBER GROWTH:")
    print("   New subscribers gained this week:")
    
    total_new_fans = 0
    total_new_clicks = 0
    total_earnings_growth = 0
    
    # Sort by fan growth (descending)
    sorted_by_fans = sorted(metrics.items(), key=lambda x: x[1]['fan_growth'], reverse=True)
    
    for link, data in sorted_by_fans:
        fan_growth = data['fan_growth']
        click_growth = data['click_growth']
        
        if fan_growth > 0:
            total_new_fans += fan_growth
            total_new_clicks += click_growth
            
            growth_indicator = "🚀" if fan_growth >= 10 else "📈" if fan_growth >= 5 else "⬆️"
            print(f"   {growth_indicator} {link}: +{fan_growth} fans (+{click_growth:,} clicks)")
    
    # 2. WEEKLY REVENUE PERFORMANCE
    print(f"\n💰 WEEKLY REVENUE PERFORMANCE:")
    print("   Revenue growth by platform:")
    
    # Sort by earnings growth (descending)
    sorted_by_earnings = sorted(metrics.items(), key=lambda x: x[1]['earnings_growth'], reverse=True)
    
    for link, data in sorted_by_earnings:
        earnings_growth = data['earnings_growth']
        current_earnings = data['current_earnings']
        earnings_per_fan = data['earnings_per_fan']
        
        if earnings_growth != 0 or current_earnings > 0:
            total_earnings_growth += earnings_growth
            
            if earnings_growth > 0:
                growth_indicator = "💰"
            elif earnings_growth < 0:
                growth_indicator = "📉"
            else:
                growth_indicator = "➡️"
            
            print(f"   {growth_indicator} {link}: ${earnings_growth:+.2f} (Total: ${current_earnings:.2f})")
            print(f"      └─ ${earnings_per_fan:.2f} per fan ({data['current_fans']} fans)")
    
    # 3. TOP PERFORMING PLATFORMS
    print(f"\n🏆 TOP PERFORMING PLATFORMS:")
    print("   Best performers across key metrics:")
    
    # Top by subscriber growth
    top_subscriber_growth = sorted_by_fans[:3]
    print("   📈 Subscriber Growth Leaders:")
    for i, (link, data) in enumerate(top_subscriber_growth, 1):
        if data['fan_growth'] > 0:
            print(f"      {i}. {link}: +{data['fan_growth']} fans")
    
    # Top by revenue
    top_revenue = sorted_by_earnings[:3]
    print("   💰 Revenue Leaders:")
    for i, (link, data) in enumerate(top_revenue, 1):
        if data['current_earnings'] > 0:
            print(f"      {i}. {link}: ${data['current_earnings']:.2f} total")
    
    # 4. CONVERSION RATE ANALYSIS
    print(f"\n📊 CONVERSION RATE ANALYSIS:")
    print("   Fan conversion efficiency:")
    
    # Sort by conversion rate (descending)
    sorted_by_conversion = sorted(metrics.items(), key=lambda x: x[1]['conversion_rate'], reverse=True)
    
    for link, data in sorted_by_conversion[:5]:  # Top 5 conversion rates
        conversion_rate = data['conversion_rate']
        if conversion_rate > 0:
            efficiency_indicator = "🎯" if conversion_rate >= 5 else "📊" if conversion_rate >= 2 else "📉"
            print(f"   {efficiency_indicator} {link}: {conversion_rate:.2f}% conversion")
            print(f"      └─ {data['current_fans']} fans from {data['current_clicks']:,} clicks")
    
    return {
        'metrics': metrics,
        'total_new_fans': total_new_fans,
        'total_new_clicks': total_new_clicks,
        'total_earnings_growth': total_earnings_growth,
        'period': f"{start_date} → {end_date}",
        'days_count': days_count
    }


def get_weekly_ai_insights(metrics):
    """Generate AI insights for weekly performance"""
    print(f"\n🤖 AI WEEKLY INSIGHTS:")
    print("   Strategic recommendations based on weekly data:")
    
    # Get AI analysis
    analytics = TrackingAnalytics()
    
    # Prepare data for AI analysis
    ai_data = {
        'weekly_performance': {},
        'revenue_analysis': {},
        'conversion_insights': {}
    }
    
    for link, data in metrics.items():
        ai_data['weekly_performance'][link] = {
            'fan_growth': data['fan_growth'],
            'click_growth': data['click_growth'],
            'earnings_growth': data['earnings_growth'],
            'conversion_rate': data['conversion_rate'],
            'earnings_per_fan': data['earnings_per_fan'],
            'current_totals': {
                'fans': data['current_fans'],
                'clicks': data['current_clicks'],
                'earnings': data['current_earnings']
            }
        }
    
    # Generate AI analysis prompt for weekly data
    prompt = f"""
    Analyze this OnlyMonster weekly performance data and provide strategic insights:
    
    Weekly Performance Summary:
    {json.dumps(ai_data, indent=2)}
    
    Please provide:
    1. Top 3 strategic opportunities for growth
    2. Revenue optimization recommendations
    3. Platform-specific improvement suggestions
    4. Conversion rate enhancement strategies
    5. Weekly trend analysis and predictions
    
    Focus on actionable insights for maximizing subscriber growth and revenue.
    """
    
    try:
        ai_analysis = analytics.call_openrouter_api(prompt)
        if ai_analysis:
            # Format AI response for display
            lines = ai_analysis.strip().split('\n')
            for line in lines:
                if line.strip():
                    print(f"   {line}")
        return ai_analysis
    except Exception as e:
        print(f"   ⚠️  AI analysis unavailable: {e}")
        return "AI analysis temporarily unavailable"


def format_weekly_slack_message(report_data, ai_insights=""):
    """Format weekly report for Slack"""
    metrics = report_data['metrics']
    
    message = "📊 *OnlyMonster Weekly Report*\n"
    message += f"📅 {report_data['period']} ({report_data['days_count']} days)\n"
    message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
    
    # Weekly Summary
    message += "📈 *WEEKLY SUMMARY*\n"
    message += f"• +{report_data['total_new_fans']} new subscribers\n"
    message += f"• +{report_data['total_new_clicks']:,} new clicks\n"
    message += f"• ${report_data['total_earnings_growth']:+.2f} revenue change\n\n"
    
    # Top Performers
    sorted_by_fans = sorted(metrics.items(), key=lambda x: x[1]['fan_growth'], reverse=True)
    message += "🏆 *TOP SUBSCRIBER GROWTH*\n"
    for link, data in sorted_by_fans[:3]:
        if data['fan_growth'] > 0:
            message += f"• {link}: +{data['fan_growth']} fans\n"
    message += "\n"
    
    # Revenue Leaders
    sorted_by_earnings = sorted(metrics.items(), key=lambda x: x[1]['current_earnings'], reverse=True)
    message += "💰 *REVENUE LEADERS*\n"
    for link, data in sorted_by_earnings[:3]:
        if data['current_earnings'] > 0:
            message += f"• {link}: ${data['current_earnings']:.2f}\n"
    message += "\n"
    
    # AI Insights (truncated for Slack)
    if ai_insights:
        message += "🤖 *AI INSIGHTS*\n"
        # Take first few lines of AI insights
        insight_lines = ai_insights.strip().split('\n')[:5]
        for line in insight_lines:
            if line.strip():
                message += f"• {line.strip()}\n"
    
    return message


def send_weekly_report_to_slack(report_data, ai_insights):
    """Send weekly report to Slack"""
    print(f"\n📤 SENDING WEEKLY REPORT TO SLACK...")
    print("-" * 35)
    
    try:
        slack_notifier = SlackWebhookNotifier()
        message = format_weekly_slack_message(report_data, ai_insights)
        
        success = slack_notifier.send_message_via_api(message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)
        
        if success:
            print("✅ Weekly report sent to Slack successfully!")
            print(f"📱 Check your channel: https://cheeksglobal.slack.com/archives/{SLACK_CHANNEL_ID}")
        else:
            print("❌ Failed to send weekly report to Slack")
            
        return success
        
    except Exception as e:
        print(f"❌ Slack error: {e}")
        return False


def main():
    """Main function to generate and send weekly report"""
    try:
        # Generate the weekly report
        report_data = generate_weekly_report()
        
        if not report_data:
            print("❌ Unable to generate weekly report - insufficient data")
            return
        
        # Get AI insights
        ai_insights = get_weekly_ai_insights(report_data['metrics'])
        
        # Print summary
        print(f"\n📈 WEEKLY SUMMARY:")
        print(f"   Total Growth: +{report_data['total_new_fans']} fans, +{report_data['total_new_clicks']:,} clicks")
        print(f"   Revenue Change: ${report_data['total_earnings_growth']:+.2f}")
        print(f"   Active Platforms: {len([l for l, d in report_data['metrics'].items() if d['fan_growth'] > 0])}/{len(PRIORITY_LINKS)}")
        
        # Send to Slack
        slack_success = send_weekly_report_to_slack(report_data, ai_insights)
        
        if slack_success:
            print("\n✅ Weekly report completed and sent to Slack!")
        else:
            print("\n⚠️  Weekly report completed but Slack delivery failed")
            
    except Exception as e:
        print(f"❌ Error generating weekly report: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
