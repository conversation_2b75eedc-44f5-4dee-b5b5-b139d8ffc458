#!/usr/bin/env python3
"""
Weekly OnlyMonster Report Scheduler
Runs weekly reports and handles automation
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import schedule
import time
import logging
from datetime import datetime
from automation.weekly_report import main as generate_weekly_report
from slack.slack_webhook import SlackWebhookNotifier
from core.config import SLACK_BOT_TOKEN, SLACK_CHANNEL_ID

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('weekly_scheduler.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def run_weekly_report():
    """Run the weekly report with error handling"""
    try:
        logger.info("🔄 Starting weekly OnlyMonster report...")
        
        # Generate and send the weekly report
        generate_weekly_report()
        
        logger.info("✅ Weekly report completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Weekly report failed: {e}")
        
        # Send error notification to <PERSON>lack
        try:
            slack_notifier = SlackWebhookNotifier()
            error_message = f"⚠️ *Weekly Report Failed*\n\n"
            error_message += f"Error: {str(e)}\n"
            error_message += f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            error_message += "Please check the logs and run manually if needed."
            
            slack_notifier.send_message_via_api(error_message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)
            logger.info("📤 Error notification sent to Slack")
            
        except Exception as slack_error:
            logger.error(f"❌ Failed to send error notification to Slack: {slack_error}")


def main():
    """Main scheduler function"""
    logger.info("🚀 Starting OnlyMonster Weekly Report Scheduler")
    logger.info("📅 Weekly reports will run every Sunday at 9:00 AM")
    
    # Schedule weekly report for Sunday at 9:00 AM
    schedule.every().sunday.at("09:00").do(run_weekly_report)
    
    # Also allow manual trigger for testing
    logger.info("💡 To run manually, you can also call weekly_report.py directly")
    
    # Send startup notification to Slack
    try:
        slack_notifier = SlackWebhookNotifier()
        startup_message = "🤖 *Weekly Report Scheduler Started*\n\n"
        startup_message += "📅 Weekly reports scheduled for Sundays at 9:00 AM\n"
        startup_message += f"🕐 Next report: {schedule.next_run()}\n"
        startup_message += "✅ System is ready and monitoring"
        
        slack_notifier.send_message_via_api(startup_message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)
        logger.info("📤 Startup notification sent to Slack")
        
    except Exception as e:
        logger.warning(f"⚠️ Could not send startup notification: {e}")
    
    # Keep the scheduler running
    while True:
        try:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
            
        except KeyboardInterrupt:
            logger.info("🛑 Weekly scheduler stopped by user")
            break
        except Exception as e:
            logger.error(f"❌ Scheduler error: {e}")
            time.sleep(300)  # Wait 5 minutes before retrying


if __name__ == "__main__":
    main()
