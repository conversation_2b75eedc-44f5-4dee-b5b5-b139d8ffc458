"""
Configuration file for OnlyMonster automation
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# OnlyMonster credentials
EMAIL = "<EMAIL>"
PASSWORD = "Shutup12!*"

# URLs
LOGIN_URL = "https://onlymonster.ai/auth/signin"
TRACKING_LINKS_URL = "https://onlymonster.ai/panel/tracking-links"
DASHBOARD_URL = "https://onlymonster.ai/panel/dashboard"

# Paths
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Database configuration
# Single source of truth for the DB file path across the app.
# Default to the repo root DB file so local runs are consistent.
# Allow overriding via env var (e.g., in Docker: /app/data/onlymonster_data.db).
DEFAULT_DB_PATH = os.path.join(BASE_DIR, "onlymonster_data.db")
DATABASE_PATH = os.getenv("DATABASE_PATH", DEFAULT_DB_PATH)

# Selenium configuration
# Allow env override: HEADLESS=true/false
HEADLESS = os.getenv('HEADLESS', 'false').lower() == 'true'
IMPLICIT_WAIT = 10  # seconds
PAGE_LOAD_TIMEOUT = 30  # seconds

# OpenRouter API configuration
OPENROUTER_API_KEY = "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"

# Priority tracking links for focused analysis
PRIORITY_LINKS = [
    "reels-naominoface",
    "reddit-babycheeksx-combined",  # Combined reddit links
    "reels-lilfoxnaomi-aug-22",
    "chive-nyla-aug-8",
    "tiktok-aug-1-24",
    "chive-aug-1-24",
    "Reels2024",
    "X - naomifoxxxx",
    "X - nylabaexo",
    "instagram-naomifunxo",  # Fixed spelling
    "naominixo-threads"
]

# Link combinations - links that should be combined for analysis
COMBINED_LINKS = {
    "reddit-babycheeksx-combined": ["reddit-babycheeksx", "reddit-babycheeksx-2"]
}

# Slack configuration
SLACK_BOT_TOKEN = "*********************************************************"
SLACK_REFRESH_TOKEN = "xoxe-1-My0xLTkwMTU1MDUzMjI3MDktOTAwMjA1ODAxMTQ5NS05MDAyMDU4MDU3NDE1LTkzNzk3YjAzMmM0MGI5NDI2NWZmNDA2MDY2ZGU4OTMxYzJlNjIxNGY2YWI0MmIzZGQ3ZDhkODRlNTlkYzZlM2U"
SLACK_APP_TOKEN = "xapp-1-A0917GCNQ1W-9010643051267-734bd5c0a0c3939c868b44df82f84249e4983d055d711c7cf10079d46f72cefc"
SLACK_CHANNEL_ID = "C090FEVA18D"  # Extracted from your channel URL

# Slack app credentials
SLACK_APP_ID = "A0917GCNQ1W"
SLACK_CLIENT_ID = "*************.*************"
SLACK_CLIENT_SECRET = "185f6724cc4efcebc25d2442c76363c4"
SLACK_SIGNING_SECRET = "1fa923b12c32bfcc3e113c1856b42102"
SLACK_VERIFICATION_TOKEN = "tBasALwVIzj8HiFfr4U7XuM9"

# Social Media Monitoring Configuration
# TikTok API Settings
RAPIDAPI_KEY = os.getenv('RAPIDAPI_KEY', '**************************************************')

# Multiple TikTok Accounts to Monitor
TIKTOK_ACCOUNTS = [
    {
        'username': 'naomifoxes',
        'secUid': 'MS4wLjABAAAAa87Em-uSLvPBWUtYIItk5wy_RbmotwT1Z-VUBhqrWN9ie-_4cFH2pAQxogrHWaWH',
        'url': 'https://www.tiktok.com/@naomifoxes'
    },
    {
        'username': 'itsnaomifox',
        'secUid': 'MS4wLjABAAAAJZ9YAdykfgMe2R0RynUFCnUtXi3OneYGZe64dwJ-E8R-AHHCzUNN-6MGTA1mlFgd',
        'url': 'https://www.tiktok.com/@itsnaomifox'
    }
]

# Instagram Accounts to Monitor
INSTAGRAM_ACCOUNTS = ['naomifoxes', 'alwaysnaomixo']

# Reddit Accounts to Monitor
REDDIT_ACCOUNTS = ['u/babycheeksx']

# Postpone.app API Settings
POSTPONE_API_KEY = "b953q4baKDCVBSbLKbDqWnvV9Dbw14tguQSwl-hyCelLMCwEhSW4z0CF5ERYh6JO17A"
POSTPONE_API_URL = "https://api.postpone.app/gql"

# Browser Settings for Social Media
HEADLESS_BROWSER = os.getenv('HEADLESS_BROWSER', 'true').lower() == 'true'
BROWSER_TIMEOUT = int(os.getenv('BROWSER_TIMEOUT', '30'))

# Persisted Selenium Chrome profile to keep OnlyMonster sessions across runs
SELENIUM_PROFILE_DIR = os.getenv('SELENIUM_PROFILE_DIR', os.path.join(BASE_DIR, '.selenium-profile'))

# OnlyMonster session cookie storage (to avoid repeated logins/challenges)
ONLYMONSTER_COOKIE_FILE = os.getenv('ONLYMONSTER_COOKIE_FILE', os.path.join(BASE_DIR, 'onlymonster_cookies.json'))

# Status Emojis
SUCCESS_EMOJI = "✅"
FAILURE_EMOJI = "❌"

# Console Output (for notifier)
CONSOLE_OUTPUT = True
