"""
Database operations for OnlyMonster tracking data
"""
import sqlite3
import datetime
from typing import List, Tuple
from core.config import DATABASE_PATH


class TrackingDatabase:
    def __init__(self):
        self.db_path = DATABASE_PATH
        self.init_database()

    def get_connection(self):
        """Return a sqlite3 connection using the configured DB path.
        Allows patterns like: `with db.get_connection() as conn:`
        """
        return sqlite3.connect(self.db_path)

    def init_database(self):
        """Initialize the database and create tables if they don't exist"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tracking_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    tracking_link_name TEXT NOT NULL,
                    clicks INTEGER NOT NULL,
                    fans INTEGER NOT NULL,
                    earnings DECIMAL(10,2) DEFAULT 0.00,
                    timestamp DATETIME NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Check if earnings column exists, if not add it
            cursor.execute("PRAGMA table_info(tracking_data)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'earnings' not in columns:
                cursor.execute('ALTER TABLE tracking_data ADD COLUMN earnings DECIMAL(10,2) DEFAULT 0.00')
                print("Added earnings column to existing tracking_data table")

            # Remove revenue column if it exists (cleanup)
            if 'revenue' in columns:
                # SQLite doesn't support DROP COLUMN directly, so we need to recreate the table
                cursor.execute('''
                    CREATE TABLE tracking_data_new (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        tracking_link_name TEXT NOT NULL,
                        clicks INTEGER NOT NULL,
                        fans INTEGER NOT NULL,
                        earnings DECIMAL(10,2) DEFAULT 0.00,
                        timestamp DATETIME NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                cursor.execute('''
                    INSERT INTO tracking_data_new (id, tracking_link_name, clicks, fans, earnings, timestamp, created_at)
                    SELECT id, tracking_link_name, clicks, fans, earnings, timestamp, created_at
                    FROM tracking_data
                ''')
                cursor.execute('DROP TABLE tracking_data')
                cursor.execute('ALTER TABLE tracking_data_new RENAME TO tracking_data')
                print("Removed revenue column from tracking_data table")

            # Create OnlyMonster dashboard revenue metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS om_dashboard_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    total_revenue REAL DEFAULT 0,
                    sales_revenue REAL DEFAULT 0,
                    subscription_revenue REAL DEFAULT 0,
                    arppu REAL DEFAULT 0,
                    timestamp DATETIME NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')


            # Create social media history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS social_media_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    platform TEXT NOT NULL,
                    username TEXT NOT NULL,
                    post_count INTEGER NOT NULL DEFAULT 0,
                    removed_posts INTEGER DEFAULT 0,
                    post_times TEXT,
                    removal_reasons TEXT,
                    status TEXT,
                    error_message TEXT,
                    check_date DATE NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Create OnlyFans deal status table (historical log)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS onlyfans_deal_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL,
                    has_deal INTEGER NOT NULL DEFAULT 0,
                    deal_percentage INTEGER DEFAULT 0,
                    deal_text TEXT,
                    checked_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.commit()

    def insert_tracking_data(self, data: List[Tuple]):
        """
        Insert tracking data into the database

        Args:
            data: List of tuples containing:
                  - (tracking_link_name, clicks, fans) - old format
                  - (tracking_link_name, clicks, fans, earnings) - with earnings from scraper
        """
        timestamp = datetime.datetime.now()

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Prepare data with timestamp, handling multiple formats
            data_with_timestamp = []
            for item in data:
                if len(item) == 3:
                    # Old format: (name, clicks, fans)
                    name, clicks, fans = item
                    earnings = 0.00
                elif len(item) == 4:
                    # New format with earnings: (name, clicks, fans, earnings)
                    name, clicks, fans, earnings = item
                else:
                    raise ValueError(f"Invalid data format: {item}")

                data_with_timestamp.append((name, clicks, fans, earnings, timestamp))

            cursor.executemany('''
                INSERT INTO tracking_data (tracking_link_name, clicks, fans, earnings, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', data_with_timestamp)

            conn.commit()
            print(f"Inserted {len(data)} records at {timestamp}")

    def insert_tracking_data_with_timestamp(self, data, custom_timestamp):
        """
        Insert tracking data with a custom timestamp

        Args:
            data: List of tuples (name, clicks, fans, earnings)
            custom_timestamp: Custom timestamp string (e.g., '2025-03-15 12:00:00')
        """
        if not data:
            print("No data to insert")
            return

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Prepare data with custom timestamp
            data_with_timestamp = []
            for item in data:
                if len(item) == 3:
                    # Old format: (name, clicks, fans)
                    name, clicks, fans = item
                    earnings = 0.00
                elif len(item) == 4:
                    # New format with earnings: (name, clicks, fans, earnings)
                    name, clicks, fans, earnings = item
                    if earnings is None:
                        earnings = 0.00
                else:
                    raise ValueError(f"Invalid data format: {item}")

                data_with_timestamp.append((name, clicks, fans, earnings, custom_timestamp))

            cursor.executemany('''
                INSERT INTO tracking_data (tracking_link_name, clicks, fans, earnings, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', data_with_timestamp)

            conn.commit()
            print(f"Inserted {len(data)} records at {custom_timestamp}")

    def get_latest_data(self, limit: int = 10):
        """Get the most recent tracking data"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, earnings, timestamp
                FROM tracking_data
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (limit,))
            return cursor.fetchall()

    def get_data_by_date(self, date: str):
        """Get tracking data for a specific date (YYYY-MM-DD format)"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, earnings, timestamp
                FROM tracking_data
                WHERE DATE(timestamp) = ?
                ORDER BY timestamp DESC
            ''', (date,))
            return cursor.fetchall()

    def get_data_by_timeframe(self, hours_back: int):
        """Get tracking data from X hours ago"""
        cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=hours_back)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, earnings, timestamp
                FROM tracking_data
                WHERE timestamp >= ?
                ORDER BY timestamp DESC
            ''', (cutoff_time,))
            return cursor.fetchall()

    def get_data_by_date_range(self, start_timestamp: str, end_timestamp: str):
        """Get tracking data for an inclusive timestamp range.

        Args:
            start_timestamp: ISO-like timestamp string (e.g., '2025-03-15 00:00:00')
            end_timestamp: ISO-like timestamp string (e.g., '2025-03-15 23:59:59')
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, earnings, timestamp
                FROM tracking_data
                WHERE timestamp >= ? AND timestamp <= ?
                ORDER BY timestamp DESC
            ''', (start_timestamp, end_timestamp))
            return cursor.fetchall()



    def update_earnings(self, tracking_link_name: str, earnings: float, timestamp: str = None):
        """
        Update earnings for a specific tracking link and timestamp

        Args:
            tracking_link_name: Name of the tracking link
            earnings: Earnings amount to set
            timestamp: Specific timestamp (optional, uses latest if not provided)
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            if timestamp:
                cursor.execute('''
                    UPDATE tracking_data
                    SET earnings = ?
                    WHERE tracking_link_name = ? AND timestamp = ?
                ''', (earnings, tracking_link_name, timestamp))
            else:
                # Update the most recent record for this link
                cursor.execute('''
                    UPDATE tracking_data
                    SET earnings = ?
                    WHERE tracking_link_name = ? AND timestamp = (
                        SELECT MAX(timestamp)
                        FROM tracking_data
                        WHERE tracking_link_name = ?
                    )
                ''', (earnings, tracking_link_name, tracking_link_name))

            conn.commit()
            print(f"Updated earnings for {tracking_link_name}: ${earnings:.2f}")



    def get_earnings_summary(self, days_back: int = 7):
        """Get earnings summary for the last N days"""
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_back)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT
                    tracking_link_name,
                    SUM(earnings) as total_earnings,
                    COUNT(*) as data_points,
                    AVG(earnings) as avg_earnings,
                    MAX(earnings) as max_earnings,
                    MIN(timestamp) as first_date,
                    MAX(timestamp) as last_date
                FROM tracking_data
                WHERE timestamp >= ?
                GROUP BY tracking_link_name
                ORDER BY total_earnings DESC
            ''', (cutoff_date,))
            return cursor.fetchall()

    def insert_om_dashboard_metrics(self, total_revenue: float, sales_revenue: float, subscription_revenue: float, arppu: float, timestamp: str = None):
        """Insert a single snapshot of OnlyMonster dashboard revenue metrics."""
        ts = timestamp or datetime.datetime.now()
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO om_dashboard_metrics (total_revenue, sales_revenue, subscription_revenue, arppu, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', (total_revenue, sales_revenue, subscription_revenue, arppu, ts))
            conn.commit()

    def get_latest_om_dashboard_metrics(self):
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT total_revenue, sales_revenue, subscription_revenue, arppu, timestamp
                FROM om_dashboard_metrics
                ORDER BY timestamp DESC
                LIMIT 1
            ''')
            return cursor.fetchone()

    def get_daily_earnings_changes(self):
        """Get daily earnings changes for all tracking links"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Get current earnings (most recent)
            cursor.execute('''
                SELECT tracking_link_name, earnings, fans
                FROM tracking_data t1
                WHERE timestamp = (
                    SELECT MAX(timestamp)
                    FROM tracking_data t2
                    WHERE t2.tracking_link_name = t1.tracking_link_name
                )
                ORDER BY earnings DESC
            ''')
            current_earnings = cursor.fetchall()

            # Get previous earnings (second most recent)
            cursor.execute('''
                SELECT DISTINCT tracking_link_name, earnings
                FROM tracking_data t1
                WHERE timestamp = (
                    SELECT MAX(timestamp)
                    FROM tracking_data t2
                    WHERE t2.tracking_link_name = t1.tracking_link_name
                    AND t2.timestamp < (
                        SELECT MAX(timestamp)
                        FROM tracking_data t3
                        WHERE t3.tracking_link_name = t1.tracking_link_name
                    )
                )
            ''')
            previous_earnings = {name: earnings for name, earnings in cursor.fetchall()}

            # Calculate daily changes
            daily_changes = []
            total_daily_change = 0

            for name, current, fans in current_earnings:
                previous = previous_earnings.get(name, 0)
                daily_change = current - previous
                total_daily_change += daily_change

                if daily_change != 0:  # Only include links with changes
                    daily_changes.append({
                        'tracking_link_name': name,
                        'daily_change': daily_change,
                        'current_total': current,
                        'fans': fans,
                        'earnings_per_fan': current / fans if fans > 0 else 0
                    })

            # Sort by daily change (descending)
            daily_changes.sort(key=lambda x: x['daily_change'], reverse=True)

            return {
                'daily_changes': daily_changes,
                'total_daily_change': total_daily_change,
                'top_3_increases': [item for item in daily_changes if item['daily_change'] > 0][:3]
            }

    def delete_rows_by_id_range(self, start_id: int, end_id: int):
        """
        Delete rows by ID range (inclusive)

        Args:
            start_id: Starting row ID to delete
            end_id: Ending row ID to delete (inclusive)
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # First, get the count of rows that will be deleted for confirmation
            cursor.execute('''
                SELECT COUNT(*) FROM tracking_data
                WHERE id >= ? AND id <= ?
            ''', (start_id, end_id))
            count = cursor.fetchone()[0]

            if count == 0:
                print(f"No rows found in ID range {start_id}-{end_id}")
                return 0

            # Delete the rows
            cursor.execute('''
                DELETE FROM tracking_data
                WHERE id >= ? AND id <= ?
            ''', (start_id, end_id))

            conn.commit()
            deleted_count = cursor.rowcount
            print(f"Deleted {deleted_count} rows (IDs {start_id}-{end_id})")
            return deleted_count

    def get_all_data_with_ids(self):
        """Get all tracking data with row IDs for debugging"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, tracking_link_name, clicks, fans, earnings, timestamp
                FROM tracking_data
                ORDER BY id DESC
            ''')
            return cursor.fetchall()

    def insert_social_media_history(self, social_data, check_date):
        """
        Insert social media check results into history table

        Args:
            social_data: Dictionary containing social media check results
            check_date: Date of the check (YYYY-MM-DD format)
        """
        import json

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Clear existing data for this date to avoid duplicates
            cursor.execute('''
                DELETE FROM social_media_history
                WHERE check_date = ?
            ''', (check_date,))

            records_inserted = 0

            # Process each platform
            for platform, platform_data in social_data.get('results', {}).items():
                if platform in ['tiktok', 'instagram', 'reddit']:

                    if platform == 'tiktok':
                        # Process TikTok accounts
                        accounts_data = platform_data.get('summary', {})
                        for username, account_info in accounts_data.items():
                            if isinstance(account_info, dict):
                                post_times = json.dumps(account_info.get('post_times', []))
                                cursor.execute('''
                                    INSERT INTO social_media_history
                                    (platform, username, post_count, post_times, status, check_date)
                                    VALUES (?, ?, ?, ?, ?, ?)
                                ''', (
                                    'tiktok',
                                    username,
                                    account_info.get('posts_count', 0),
                                    post_times,
                                    'active' if account_info.get('posts_count', 0) > 0 else 'inactive',
                                    check_date
                                ))
                                records_inserted += 1

                    elif platform == 'instagram':
                        # Process Instagram accounts
                        accounts_data = platform_data.get('account_details', {})
                        for username, account_info in accounts_data.items():
                            post_times = json.dumps(account_info.get('post_times', []))
                            error_msg = account_info.get('error', '')
                            cursor.execute('''
                                INSERT INTO social_media_history
                                (platform, username, post_count, post_times, status, error_message, check_date)
                                VALUES (?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                'instagram',
                                username,
                                account_info.get('post_count', 0),
                                post_times,
                                'active' if account_info.get('post_count', 0) > 0 else 'inactive',
                                error_msg,
                                check_date
                            ))
                            records_inserted += 1

                    elif platform == 'reddit':
                        # Process Reddit accounts
                        accounts_data = platform_data.get('account_details', {})
                        for username, account_info in accounts_data.items():
                            post_times = json.dumps(account_info.get('post_times', []))
                            removal_reasons = json.dumps(account_info.get('details', {}).get('removal_reasons', {}))
                            cursor.execute('''
                                INSERT INTO social_media_history
                                (platform, username, post_count, removed_posts, post_times, removal_reasons, status, check_date)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                'reddit',
                                username,
                                account_info.get('post_count', 0),
                                account_info.get('removed_count', 0),
                                post_times,
                                removal_reasons,
                                'active' if account_info.get('post_count', 0) > 0 else 'inactive',
                                check_date
                            ))
                            records_inserted += 1

            conn.commit()
            print(f"Inserted {records_inserted} social media history records for {check_date}")
            return records_inserted

    def get_social_media_history(self, date=None, days_back=7):
        """
        Get social media history data

        Args:
            date: Specific date (YYYY-MM-DD format) or None for recent data
            days_back: Number of days to look back if date is None
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            if date:
                cursor.execute('''
                    SELECT platform, username, post_count, removed_posts, post_times,
                           removal_reasons, status, error_message, check_date
                    FROM social_media_history
                    WHERE check_date = ?
                    ORDER BY platform, username
                ''', (date,))
            else:
                cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_back)
                cursor.execute('''
                    SELECT platform, username, post_count, removed_posts, post_times,
                           removal_reasons, status, error_message, check_date
                    FROM social_media_history
                    WHERE check_date >= ?
                    ORDER BY check_date DESC, platform, username
                ''', (cutoff_date.strftime('%Y-%m-%d'),))

            return cursor.fetchall()

    def get_available_social_dates(self):
        """Get list of dates that have social media data"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT DISTINCT check_date
                FROM social_media_history
                ORDER BY check_date DESC
            ''')
            return [row[0] for row in cursor.fetchall()]

    # ---------- OnlyFans deal status helpers ----------
    def upsert_onlyfans_deal(self, username: str, deal_info: dict, checked_at: str = None):
        """Insert latest OnlyFans deal status for a username.
        Keeps history by inserting rows rather than updating in place.
        """
        has_deal = 1 if deal_info.get('has_deal') else 0
        deal_pct = int(deal_info.get('deal_percentage') or 0)
        deal_text = deal_info.get('deal_text') or ''
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            if checked_at:
                cursor.execute('''
                    INSERT INTO onlyfans_deal_status (username, has_deal, deal_percentage, deal_text, checked_at)
                    VALUES (?, ?, ?, ?, ?)
                ''', (username, has_deal, deal_pct, deal_text, checked_at))
            else:
                cursor.execute('''
                    INSERT INTO onlyfans_deal_status (username, has_deal, deal_percentage, deal_text)
                    VALUES (?, ?, ?, ?)
                ''', (username, has_deal, deal_pct, deal_text))
            conn.commit()

    def get_latest_onlyfans_deal(self, username: str):
        """Fetch latest cached deal status for username.
        Returns dict or None if not found.
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT has_deal, deal_percentage, deal_text, checked_at
                FROM onlyfans_deal_status
                WHERE username = ?
                ORDER BY datetime(checked_at) DESC
                LIMIT 1
            ''', (username,))
            row = cursor.fetchone()
            if not row:
                return None
            has_deal, pct, text, ts = row
            return {
                'has_deal': bool(has_deal),
                'deal_percentage': pct or 0,
                'deal_text': text or '',
                'checked_at': ts,
            }

    def prune_onlyfans_deals(self, days_to_keep: int = 30) -> int:
        """Delete old OnlyFans deal rows older than N days. Returns count deleted."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                DELETE FROM onlyfans_deal_status
                WHERE datetime(checked_at) < datetime('now', ?)
            ''', (f'-{days_to_keep} days',))
            conn.commit()
            return cursor.rowcount
