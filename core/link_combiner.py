"""
Link combiner utility for OnlyMonster tracking data
Handles combining data from multiple related links (e.g., reddit-babycheeksx and reddit-babycheeksx-2)
"""
import sqlite3
from typing import Dict, List, Tuple
from core.config import DATABASE_PATH, COMBINED_LINKS


class LinkCombiner:
    def __init__(self):
        self.db_path = DATABASE_PATH
        self.combined_links = COMBINED_LINKS
    
    def combine_latest_data(self, raw_data: List[Tuple]) -> List[Tuple]:
        """
        Combine latest data for linked tracking sources

        Args:
            raw_data: List of tuples with various formats:
                     - (tracking_link_name, clicks, fans, timestamp) - old format
                     - (tracking_link_name, clicks, fans, earnings, timestamp) - with earnings

        Returns:
            List of tuples with combined data
        """
        # Convert to dict for easier processing
        data_dict = {}
        for item in raw_data:
            if len(item) == 4:
                # Old format: (name, clicks, fans, timestamp)
                name, clicks, fans, timestamp = item
                earnings = 0.00
            elif len(item) == 5:
                # With earnings: (name, clicks, fans, earnings, timestamp)
                name, clicks, fans, earnings, timestamp = item
            else:
                raise ValueError(f"Invalid data format: {item}")

            data_dict[name] = (clicks, fans, earnings, timestamp)
        
        # Process combinations
        combined_data = {}
        processed_links = set()
        
        for combined_name, source_links in self.combined_links.items():
            total_clicks = 0
            total_fans = 0
            total_earnings = 0.00
            latest_timestamp = None

            # Sum data from all source links
            for source_link in source_links:
                if source_link in data_dict:
                    clicks, fans, earnings, timestamp = data_dict[source_link]
                    total_clicks += clicks
                    total_fans += fans
                    total_earnings += earnings

                    # Use the latest timestamp
                    if latest_timestamp is None or timestamp > latest_timestamp:
                        latest_timestamp = timestamp

                    processed_links.add(source_link)

            # Only add combined data if we found at least one source link
            if total_clicks > 0 or total_fans > 0:
                combined_data[combined_name] = (total_clicks, total_fans, total_earnings, latest_timestamp)

        # Add non-combined links
        for name, (clicks, fans, earnings, timestamp) in data_dict.items():
            if name not in processed_links:
                combined_data[name] = (clicks, fans, earnings, timestamp)

        # Convert back to list of tuples
        return [(name, clicks, fans, earnings, timestamp) for name, (clicks, fans, earnings, timestamp) in combined_data.items()]
    
    def combine_historical_data(self, data_by_link: Dict) -> Dict:
        """
        Combine historical data for linked tracking sources
        
        Args:
            data_by_link: Dict with link names as keys and list of historical data as values
            
        Returns:
            Dict with combined historical data
        """
        combined_data = {}
        processed_links = set()
        
        for combined_name, source_links in self.combined_links.items():
            # Get all timestamps from all source links
            all_timestamps = set()
            for source_link in source_links:
                if source_link in data_by_link:
                    for entry in data_by_link[source_link]:
                        all_timestamps.add(entry['timestamp'])
                    processed_links.add(source_link)
            
            # For each timestamp, sum the data from all source links
            combined_entries = []
            for timestamp in sorted(all_timestamps, reverse=True):
                total_clicks = 0
                total_fans = 0
                total_earnings = 0.0

                for source_link in source_links:
                    if source_link in data_by_link:
                        # Find entry for this timestamp
                        for entry in data_by_link[source_link]:
                            if entry['timestamp'] == timestamp:
                                total_clicks += entry['clicks']
                                total_fans += entry['fans']
                                total_earnings += entry.get('earnings', 0.0)
                                break

                if total_clicks > 0 or total_fans > 0:
                    combined_entries.append({
                        'clicks': total_clicks,
                        'fans': total_fans,
                        'earnings': total_earnings,
                        'timestamp': timestamp
                    })
            
            if combined_entries:
                combined_data[combined_name] = combined_entries
        
        # Add non-combined links
        for name, entries in data_by_link.items():
            if name not in processed_links:
                combined_data[name] = entries
        
        return combined_data
    
    def get_combined_comparison_data(self):
        """Get current vs previous data with combinations applied"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Get current data (latest timestamp)
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, timestamp
                FROM tracking_data t1
                WHERE timestamp = (
                    SELECT MAX(timestamp) 
                    FROM tracking_data t2 
                    WHERE t2.tracking_link_name = t1.tracking_link_name
                )
            ''')
            current_raw = cursor.fetchall()
            current_combined = self.combine_latest_data(current_raw)
            current_data = {name: (clicks, fans) for name, clicks, fans, _ in current_combined}
            
            # Get previous data (second latest timestamp)
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, timestamp
                FROM tracking_data
                WHERE timestamp < (SELECT MAX(timestamp) FROM tracking_data)
                ORDER BY timestamp DESC
            ''')
            
            previous_raw = cursor.fetchall()
            # Group by link name and take the most recent for each
            previous_by_link = {}
            for name, clicks, fans, timestamp in previous_raw:
                if name not in previous_by_link:
                    previous_by_link[name] = (clicks, fans, timestamp)
            
            previous_combined = self.combine_latest_data([(name, clicks, fans, timestamp) 
                                                        for name, (clicks, fans, timestamp) in previous_by_link.items()])
            previous_data = {name: (clicks, fans) for name, clicks, fans, _ in previous_combined}
            
            return current_data, previous_data
    
    def get_display_name(self, link_name: str) -> str:
        """Get display name for a link (shows individual links for combined entries)"""
        for combined_name, source_links in self.combined_links.items():
            if link_name == combined_name:
                return f"{' + '.join(source_links)}"
        return link_name
