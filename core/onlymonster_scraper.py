"""
OnlyMonster.ai Tracking Links Scraper
Automates login and data extraction from tracking links page
"""
import time
import os
import re
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import T
from seleniumbase import SB
from core.database import TrackingDatabase
from core.analytics import TrackingAnalytics
from core.config import EMAIL, PASSWORD, LOGIN_URL, TRACKING_LINKS_URL, HEADLESS, ONLYMONSTER_COOKIE_FILE
from core.config import DASHBOARD_URL
import json
import re
import csv

MAXIMUM_PAGE_CHECKS = 5
os.makedirs("debug", exist_ok=True)

class OnlyMonsterScraper:
    def __init__(self):
        self.db = TrackingDatabase()


    def _detect_forbidden(self, sb) -> bool:
        try:
            html = sb.get_page_source() or ""
            signals = [
                "403 Forbidden",
                "Access denied",
                "cloudflare",  # cf error pages
                "Request blocked"
            ]
            return any(s.lower() in html.lower() for s in signals)
        except Exception:
            return False

    def load_cookies(self):
        try:
            if os.path.exists(ONLYMONSTER_COOKIE_FILE):
                with open(ONLYMONSTER_COOKIE_FILE, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Failed to load cookies: {e}")
        return []

    def __sleep_while_loading(self, sb, silent=False):
        if not silent:
            print("Waiting for page to finish loading...", end="\r")
        for __ in range(MAXIMUM_PAGE_CHECKS):
            # Check the document ready state
            page_state = sb.driver.execute_script("return document.readyState")
            if page_state == 'complete':
                break
            else:
                time.sleep(2)
        # check for body element
        try:
            sb.wait_for_element_present("body", timeout=15)
        except Exception:
            pass
        if not silent:
            print("✅ Waiting for page to finish loading...", end="\n")


    def save_cookies(self, sb):
        try:
            cookies = sb.get_cookies()
            with open(ONLYMONSTER_COOKIE_FILE, 'w') as f:
                json.dump(cookies, f)
            print(f"Saved session cookies to {ONLYMONSTER_COOKIE_FILE}")
        except Exception as e:
            print(f"Failed to save cookies: {e}")

    def try_cookie_login(self, sb) -> bool:
        """Attempt to restore an authenticated session using saved cookies."""
        try:
            # Helper to normalize cookie dict (support both 'expires' and 'expiry')
            def normalize_cookie(raw):
                cookie = {}
                # Map supported fields
                for k in ["name", "value", "domain", "path", "secure", "httpOnly", "sameSite", "expiry", "expires"]:
                    if k in raw:
                        cookie[k] = raw[k]
                # Convert 'expires' -> 'expiry' if needed
                if 'expiry' not in cookie and 'expires' in cookie:
                    try:
                        cookie['expiry'] = int(float(cookie['expires']))
                    except Exception:
                        pass
                    cookie.pop('expires', None)
                return cookie

            cookies = self.load_cookies()
            if not cookies:
                print("No cookies loaded from file")
                return False
            onlymonster_cookies = [cookie for cookie in cookies if "onlymonster.ai" in cookie.get('domain')]
            sb.open("https://onlymonster.ai")


            print("Loading cookies...")
            for cookie in onlymonster_cookies:
                try:
                    if "name" in cookie and "value" in cookie:
                        sb.driver.add_cookie(cookie)
                        # print(f"Added cookie: {cookie['name']}={cookie['value']}")
                except Exception as e:
                    print(f"[!] Error adding {cookie}: {e}")
                    pass
            print(f"Trying to access dashboard => {DASHBOARD_URL}")
            sb.open(DASHBOARD_URL)
            time.sleep(5)
            self.__sleep_while_loading(sb)
            for _ in range(5):
                if sb.get_current_url().endswith("panel/dashboard") or sb.get_current_url().endswith("panel/creators"):
                    print("Cookie-based session restored")
                    return True
                time.sleep(1)
            else:
                print(f"Cookie restore failed; current URL: {sb.get_current_url()}")
                sb.driver.delete_all_cookies()

            return False
        except Exception as e:
            print(f"Cookie restore exception: {e}")
            return False

    def login(self, sb):
        """Login to OnlyMonster.ai with Clerk.js authentication"""
        if not sb.get_current_url().endswith("/auth/signin") or self._detect_forbidden(sb):
            print("Navigating to OnlyMonster.ai sign-in page...")
            sb.uc_open_with_reconnect(LOGIN_URL, 3)

        try:
            sb.wait_for_element_present("#sign-in-block", timeout=15)
            if sb.is_element_present("#sign-in-block input[type='email'], #sign-in-block input[name='identifier']"):
                sb.type("#sign-in-block input[type='email'], #sign-in-block input[name='identifier']", EMAIL + "\n")
            print("Email entered")
            time.sleep(3)

            # Step 2: Wait for password field and enter password
            print("Step 2: Waiting for password field...")
            self.__sleep_while_loading(sb)
            sb.wait_for_element_present("#sign-in-block", timeout=15)

            if sb.is_element_present("#sign-in-block input[type='password'], #sign-in-block input[name='password']"):
                sb.type("#sign-in-block input[type='password'], #sign-in-block input[name='password']", PASSWORD + "\n")

            print("Password entered successfully")
            time.sleep(3)

            print("Waiting for redirect to panel...")
            self.__sleep_while_loading(sb)
            try:
                for _ in range(30):
                    if ("panel" in sb.get_current_url().lower() or "dashboard" in sb.get_current_url().lower()):
                        print("Login successful!")
                        break
                    time.sleep(1)
                else:
                    raise Exception("Login failed")
            except Exception:
                print(f"Redirect timeout. Current URL: {sb.get_current_url()}")
                # Check if we're still on auth page or if there's an error
                if "auth" in sb.get_current_url():
                    print("Still on auth page, checking for errors...")
                    # Look for error messages
                    try:
                        error_elements = sb.find_elements("[role='alert'], .error, .cl-formFieldError")
                        if error_elements:
                            for error in error_elements:
                                if error.text.strip():
                                    print(f"Error found: {error.text}")
                    except:
                        pass
                raise

        except Exception:
            print("Login failed - timeout waiting for elements")
            # Save page source for debugging
            with open("debug/login_debug.html", "w", encoding="utf-8") as f:
                f.write(sb.get_page_source())
            print("Page source saved to debug/login_debug.html for debugging")
            raise


    def take_debug_screenshot(self, sb, filename):
        """Take a screenshot for debugging purposes"""
        try:
            sb.save_screenshot(filename)
            print(f"Debug screenshot saved: {filename}")
        except Exception as e:
            print(f"Could not save screenshot: {e}")

    def load_all_tracking_links(self, sb):
        """Click 'Load More' button until all tracking links are loaded"""
        print("Loading all tracking links...")

        # Take initial screenshot
        self.take_debug_screenshot(sb, "debug/before_load_more.png")

        # Click load more button
        wait_timeout = 8
        while True:
            try:
                sb.wait_for_element_present("//button[contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'load more')]", timeout=wait_timeout)
                sb.click("//button[contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'load more')]")
                print("Clicked load more button")
                wait_timeout = 3
            except Exception as e:
                print(f"Error loading more tracking links: {e}")
                break

        # Take final screenshot
        self.take_debug_screenshot(sb, "debug/after_load_more.png")

    def extract_tracking_data(self, sb):
        """Extract tracking links data from the page"""
        def _clean_value(value):
            return float(value.replace('$', '').replace(',', '').replace(' ', ''))

        print("Extracting tracking data...")
        sb.uc_open_with_reconnect(TRACKING_LINKS_URL, 3)
        self.__sleep_while_loading(sb)
        tracking_data = []

        try:
            # First, load all tracking links by clicking Load More buttons
            self.load_all_tracking_links(sb)

            self.__sleep_while_loading(sb)


            # Common selectors for tables and data rows
            possible_selectors = [
                "table tbody tr",
                ".tracking-link",
                ".link-item",
                "[data-tracking]",
                ".table-row",
                "tr:has(td)"
            ]

            rows = None
            for selector in possible_selectors:
                try:
                    rows = sb.find_elements(selector)
                    if rows:
                        print(f"Found {len(rows)} rows using selector: {selector}")
                        break
                except:
                    continue

            # todo fix this logic
            for row in rows:

                try:
                    name = row.find_element(By.XPATH, ".//td[1]//b").text.strip()
                    clicks = row.find_element(By.XPATH, ".//td[2]").text.strip()
                    fans = row.find_element(By.XPATH, ".//td[3]").text.strip()
                    earnings = _clean_value(row.find_element(By.XPATH, ".//td[5]").text.strip())
                    tracking_data.append((name, clicks, fans, earnings))
                    print(f"Extracted: {name} - Clicks: {clicks}, Fans: {fans}, Earnings: ${earnings:.2f}")

                except Exception as e:
                    print(f"Error processing row: {e}")
                    continue

            print(f"Successfully extracted {len(tracking_data)} tracking links")

            # save to csv file in debug folder

            with open("debug/onlymonster_tracking_data.csv", "w", newline="") as f:
                writer = csv.writer(f)
                writer.writerow(["Name", "Clicks", "Fans", "Earnings"])
                writer.writerows(tracking_data)

            # Debug: Print all extracted link names to verify we got everything
            print("\nExtracted tracking links:")
            for name, clicks, fans, earnings in tracking_data:
                print(f"  - {name}")

            # Check if we got all priority links from config
            from config import PRIORITY_LINKS
            extracted_names = [name for name, _, _, _ in tracking_data]
            missing_links = []

            for priority_link in PRIORITY_LINKS:
                # Check if priority link exists in extracted data (case-insensitive partial match)
                found = False
                for extracted_name in extracted_names:
                    if priority_link.lower() in extracted_name.lower() or extracted_name.lower() in priority_link.lower():
                        found = True
                        break
                if not found:
                    missing_links.append(priority_link)

            if missing_links:
                print(f"\n⚠️  WARNING: Missing {len(missing_links)} priority links:")
                for link in missing_links:
                    print(f"  - {link}")
                print("These links may need to be loaded with additional 'Load More' clicks or may have different names.")
            else:
                print("\n✅ All priority links found!")

            return tracking_data

        except Exception as e:
            print(f"Error extracting tracking data: {e}")
            return tracking_data

    def extract_dashboard_revenue_metrics(self, sb):
        """Extract total revenue, sales revenue, subscription revenue, and ARPPU from OnlyMonster dashboard."""

        def _clean_value(value):
            return float(value.replace('$', '').replace(',', '').replace(' ', ''))
        try:
            # Open dashboard
            print(f"\n🔍 Extracting dashboard revenue metrics...")
            if not sb.get_current_url().endswith("panel/dashboard"):
                sb.driver.get(DASHBOARD_URL)
                print("Navigating to dashboard")
                time.sleep(5)
                self.__sleep_while_loading(sb)

            total, sales, subs, arppu = 0, 0, 0, 0

            total = sb.get_text("//p[@data-toggle='tooltip' and contains(text(), 'Total Revenue')]/following-sibling::div/p[@class='value']")
            total = _clean_value(total)

            sales = sb.get_text("//p[@data-toggle='tooltip' and contains(text(), 'Sales Revenue')]/following-sibling::div/p[@class='value']")
            sales = _clean_value(sales)

            subs = sb.get_text("//p[@data-toggle='tooltip' and contains(text(), 'Subscription Revenue')]/following-sibling::div/p[@class='value']")
            subs = _clean_value(subs)

            arppu = sb.get_text("//p[@data-toggle='tooltip' and contains(text(), 'ARPPU')]/following-sibling::div/p[@class='value']")
            arppu = _clean_value(arppu)

            print(f"Extracted dashboard metrics: Total={total}, Sales={sales}, Subs={subs}, ARPPU={arppu}")
            # save to csv file in debug folder
            with open("debug/onlymonster_dashboard_metrics.csv", "w", newline="") as f:
                writer = csv.writer(f)
                writer.writerow(["Total", "Sales", "Subs", "ARPPU"])
                writer.writerow([total, sales, subs, arppu])
            return (total, sales, subs, arppu)
        except Exception as e:
            print(f"Error extracting dashboard metrics: {e}")
            return None

    def run_scraping(self):
        """Main method to run the complete scraping process"""

        # Use SeleniumBase context manager with optimal settings
        with SB(
            uc=True,               # Use undetected-chromedriver (anti-detection)
            headless=HEADLESS,     # Respect configuration for headless
            incognito=True,        # Use incognito mode for clean sessions
            ad_block_on=False,     # Keep ads for realistic browsing
            maximize=True,         # Maximize window for better element detection
        ) as sb:
            try:
                print("Starting OnlyMonster scraping...")
                # Try cookies first; if they fail, fall back to interactive Clerk login
                if not self.try_cookie_login(sb):
                    # In headless/CI environments, interactive login is brittle and often blocked.
                    # Prefer asking the operator to seed cookies via scripts/seed_onlymonster_cookies.py
                    if HEADLESS:
                        raise Exception(
                            "Cookie login failed in headless mode. Seed session cookies by running: "
                            "HEADLESS=false PYTHONPATH=. python3 scripts/seed_onlymonster_cookies.py"
                        )
                    else:
                        print("Cookie login failed; attempting interactive login…")
                        # open new tab
                        # clear all cookies
                        self.login(sb)
                        # After successful login, persist cookies for future runs
                        try:
                            self.save_cookies(sb)
                        except Exception:
                            pass

                # Also scrape dashboard revenue metrics before tracking links
                try:
                    dash_metrics = self.extract_dashboard_revenue_metrics(sb)
                    if dash_metrics:
                        tr, sr, subr, arppu = dash_metrics
                        self.db.insert_om_dashboard_metrics(tr, sr, subr, arppu)
                        print(f"Stored dashboard metrics: total={tr}, sales={sr}, subs={subr}, arppu={arppu}")
                except Exception as e:
                    print(f"Dashboard metrics scraping failed (non-fatal): {e}")

                # Navigate to tracking links


                # Extract data
                tracking_data = self.extract_tracking_data(sb)

                if tracking_data:
                    # Store in database
                    self.db.insert_tracking_data(tracking_data)
                    print(f"Successfully stored {len(tracking_data)} records in database")
                else:
                    print("No tracking data found to store")

                return tracking_data

            except Exception as e:
                print(f"Scraping failed: {e}")
                raise


if __name__ == "__main__":
    scraper = OnlyMonsterScraper()
    try:
        data = scraper.run_scraping()
        print("Scraping completed successfully!")

        # Run analytics after successful scraping
        if data:
            print("\n" + "="*60)
            print("🔍 RUNNING ANALYTICS...")
            print("="*60)

            analytics = TrackingAnalytics()
            analysis = analytics.run_analysis(24)  # Compare with 24 hours ago
            analytics.print_analysis_report(analysis)
        else:
            print("No data collected, skipping analysis.")

    except Exception as e:
        print(f"Scraping failed: {e}")
