"""
OnlyMonster.ai Tracking Links Scraper
Automates login and data extraction from tracking links page
"""
import time
import os
import re
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from seleniumbase import SB
from core.database import TrackingDatabase
from core.analytics import TrackingAnalytics
from core.config import <PERSON>MAIL, PASSWORD, LOGIN_URL, TRACKING_LINKS_URL, HEADLESS, ONLYMONSTER_COOKIE_FILE
from core.config import DASHBOARD_URL
import json
import re
import csv

MAXIMUM_PAGE_CHECKS = 5
os.makedirs("debug", exist_ok=True)

class OnlyMonsterScraper:
    def __init__(self):
        self.db = TrackingDatabase()


    def _detect_forbidden(self, sb) -> bool:
        try:
            html = sb.get_page_source() or ""
            signals = [
                "403 Forbidden",
                "Access denied",
                "cloudflare",  # cf error pages
                "Request blocked"
            ]
            return any(s.lower() in html.lower() for s in signals)
        except Exception:
            return False

    def load_cookies(self):
        try:
            if os.path.exists(ONLYMONSTER_COOKIE_FILE):
                with open(ONLYMONSTER_COOKIE_FILE, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Failed to load cookies: {e}")
        return []

    def __sleep_while_loading(self, sb, silent=False):
        if not silent:
            print("Waiting for page to finish loading...", end="\r")
        for __ in range(MAXIMUM_PAGE_CHECKS):
            # Check the document ready state
            page_state = sb.driver.execute_script("return document.readyState")
            if page_state == 'complete':
                break
            else:
                time.sleep(2)
        # check for body element
        try:
            sb.wait_for_element_present("body", timeout=15)
        except Exception:
            pass
        if not silent:
            print("✅ Waiting for page to finish loading...", end="\n")


    def save_cookies(self, sb):
        try:
            cookies = sb.get_cookies()
            with open(ONLYMONSTER_COOKIE_FILE, 'w') as f:
                json.dump(cookies, f)
            print(f"Saved session cookies to {ONLYMONSTER_COOKIE_FILE}")
        except Exception as e:
            print(f"Failed to save cookies: {e}")

    def try_cookie_login(self, sb) -> bool:
        """Attempt to restore an authenticated session using saved cookies."""
        try:
            # Helper to normalize cookie dict (support both 'expires' and 'expiry')
            def normalize_cookie(raw):
                cookie = {}
                # Map supported fields
                for k in ["name", "value", "domain", "path", "secure", "httpOnly", "sameSite", "expiry", "expires"]:
                    if k in raw:
                        cookie[k] = raw[k]
                # Convert 'expires' -> 'expiry' if needed
                if 'expiry' not in cookie and 'expires' in cookie:
                    try:
                        cookie['expiry'] = int(float(cookie['expires']))
                    except Exception:
                        pass
                    cookie.pop('expires', None)
                return cookie

            cookies = self.load_cookies()
            if not cookies:
                print("No cookies loaded from file")
                return False
            onlymonster_cookies = [cookie for cookie in cookies if "onlymonster.ai" in cookie.get('domain')]
            sb.driver.get("https://onlymonster.ai")


            print("Loading cookies...")
            for cookie in onlymonster_cookies:
                try:
                    if "name" in cookie and "value" in cookie:
                        sb.driver.add_cookie(cookie)
                        print(f"Added cookie: {cookie['name']}={cookie['value']}")
                except Exception as e:
                    print(f"[!] Error adding {cookie}: {e}")
                    pass
            print(f"Trying to access dashboard => {DASHBOARD_URL}")
            sb.driver.get(DASHBOARD_URL)
            self.__sleep_while_loading(sb)
            for _ in range(5):
                if sb.get_current_url().endswith("panel/dashboard") or sb.get_current_url().endswith("panel/creators"):
                    print("Cookie-based session restored")
                    return True
                time.sleep(1)
            else:
                print(f"Cookie restore failed; current URL: {sb.get_current_url()}")
                sb.driver.delete_all_cookies()

            return False
        except Exception as e:
            print(f"Cookie restore exception: {e}")
            return False

    def login(self, sb):
        """Login to OnlyMonster.ai with Clerk.js authentication"""
        if not sb.driver.get_current_url().endswith("/auth/signin") or self._detect_forbidden(sb):
            print("Navigating to OnlyMonster.ai sign-in page...")
            sb.uc_open_with_reconnect(LOGIN_URL, 3)

        try:
            sb.wait_for_element_present("#sign-in-block", timeout=15)
            if sb.is_element_present("#sign-in-block input[type='email'], #sign-in-block input[name='identifier']"):
                sb.type("#sign-in-block input[type='email'], #sign-in-block input[name='identifier']", EMAIL + "\n")
            print("Email entered")
            time.sleep(3)

            # Step 2: Wait for password field and enter password
            print("Step 2: Waiting for password field...")
            self.__sleep_while_loading(sb)
            sb.wait_for_element_present("#sign-in-block", timeout=15)

            if sb.is_element_present("#sign-in-block input[type='password'], #sign-in-block input[name='password']"):
                sb.type("#sign-in-block input[type='password'], #sign-in-block input[name='password']", PASSWORD + "\n")

            print("Password entered successfully")
            time.sleep(3)

            print("Waiting for redirect to panel...")
            self.__sleep_while_loading(sb)
            try:
                for _ in range(30):
                    if ("panel" in sb.get_current_url().lower() or "dashboard" in sb.get_current_url().lower()):
                        print("Login successful!")
                        break
                    time.sleep(1)
                else:
                    raise Exception("Login failed")
            except Exception:
                print(f"Redirect timeout. Current URL: {sb.get_current_url()}")
                # Check if we're still on auth page or if there's an error
                if "auth" in sb.get_current_url():
                    print("Still on auth page, checking for errors...")
                    # Look for error messages
                    try:
                        error_elements = sb.find_elements("[role='alert'], .error, .cl-formFieldError")
                        if error_elements:
                            for error in error_elements:
                                if error.text.strip():
                                    print(f"Error found: {error.text}")
                    except:
                        pass
                raise

        except Exception:
            print("Login failed - timeout waiting for elements")
            # Save page source for debugging
            with open("debug/login_debug.html", "w", encoding="utf-8") as f:
                f.write(sb.get_page_source())
            print("Page source saved to debug/login_debug.html for debugging")
            raise


    def take_debug_screenshot(self, sb, filename):
        """Take a screenshot for debugging purposes"""
        try:
            sb.save_screenshot(filename)
            print(f"Debug screenshot saved: {filename}")
        except Exception as e:
            print(f"Could not save screenshot: {e}")

    def load_all_tracking_links(self, sb):
        """Click 'Load More' button until all tracking links are loaded"""
        print("Loading all tracking links...")

        # Take initial screenshot
        self.take_debug_screenshot(sb, "debug/before_load_more.png")

        max_attempts = 10  # Prevent infinite loops
        attempts = 0

        while attempts < max_attempts:
            try:
                # Look for "Load More" button with various possible selectors
                load_more_selectors = [
                    "button:contains('Load More')",
                    "button:contains('load more')",
                    "button:contains('Show More')",
                    "button:contains('show more')",
                    ".load-more",
                    ".show-more",
                    "[data-action='load-more']",
                    "button[onclick*='load']",
                    "button[onclick*='more']"
                ]

                load_more_button = None

                # Try to find Load More button using text content
                all_clickable_elements = sb.find_elements("//button | //a | //div[@role='button'] | //span[@role='button']")
                for element in all_clickable_elements:
                    try:
                        element_text = element.text.lower().strip()
                        # Check for various Load More text patterns
                        load_more_patterns = [
                            'load more', 'show more', 'load all', 'see more',
                            'view more', 'more results', 'show all', 'load additional',
                            'expand', 'see all'
                        ]

                        if any(phrase in element_text for phrase in load_more_patterns):
                            if element.is_displayed() and element.is_enabled():
                                load_more_button = element
                                print(f"Found Load More element with text: '{element.text}' (tag: {element.tag_name})")
                                break
                    except:
                        continue

                # If not found by text, try CSS selectors
                if not load_more_button:
                    for selector in load_more_selectors:
                        try:
                            potential_buttons = sb.find_elements(selector)
                            for btn in potential_buttons:
                                if btn.is_displayed() and btn.is_enabled():
                                    load_more_button = btn
                                    print(f"Found Load More button with selector: {selector}")
                                    break
                            if load_more_button:
                                break
                        except:
                            continue

                if load_more_button:
                    # Scroll to the button to ensure it's visible
                    sb.execute_script("arguments[0].scrollIntoView(true);", load_more_button)
                    time.sleep(1)

                    # Click the Load More button
                    try:
                        load_more_button.click()
                        print(f"Clicked Load More button (attempt {attempts + 1})")
                        time.sleep(3)  # Wait for new content to load
                        attempts += 1
                    except Exception as e:
                        print(f"Error clicking Load More button: {e}")
                        break
                else:
                    print("No more Load More button found - all content loaded")
                    break

            except Exception as e:
                print(f"Error during Load More process: {e}")
                break

        print(f"Completed Load More process after {attempts} attempts")

        # Take final screenshot
        self.take_debug_screenshot(sb, "debug/after_load_more.png")

    def extract_tracking_data(self, sb):
        """Extract tracking links data from the page"""
        print("Extracting tracking data...")
        sb.uc_open_with_reconnect(TRACKING_LINKS_URL, 3)
        self.__sleep_while_loading(sb)
        tracking_data = []

        try:
            # First, load all tracking links by clicking Load More buttons
            self.load_all_tracking_links(sb)

            self.__sleep_while_loading(sb)


            # Common selectors for tables and data rows
            possible_selectors = [
                "table tbody tr",
                ".tracking-link",
                ".link-item",
                "[data-tracking]",
                ".table-row",
                "tr:has(td)"
            ]

            rows = None
            for selector in possible_selectors:
                try:
                    rows = sb.find_elements(selector)
                    if rows:
                        print(f"Found {len(rows)} rows using selector: {selector}")
                        break
                except:
                    continue

            # todo fix this logic
            for row in rows:
                try:
                    # Extract text from the row
                    row_text = row.text.strip()
                    if not row_text:
                        continue

                    # Look for patterns that might contain tracking link name, clicks, and fans
                    # This is a flexible approach that can be adjusted based on actual page structure
                    cells = row.find_elements(By.XPATH, ".//td")
                    # print("Cells:", cells)

                    if not cells:
                        cells = row.find_elements(By.XPATH, ".//div | .//span")
                        # print("Cells (div, span):", cells)

                    if len(cells) >= 4:  # Need at least 4 columns: Name, Clicks, Fans, Earnings
                        # Assume first cell is name, and look for numbers in other cells
                        raw_name = cells[0].text.strip()

                        # Clean up the name - remove "Created XX-XX-XXXX" part
                        name = raw_name.split('\nCreated')[0].strip()
                        if not name:
                            name = raw_name.split('Created')[0].strip()

                        clicks = 0
                        fans = 0
                        earnings = 0.00

                        # Debug: Print all cell contents to understand table structure
                        # print(f"DEBUG - Row cells ({len(cells)} total):")
                        for i, cell in enumerate(cells):
                            cell_text = cell.text.strip()
                            # print(f"  Column {i}: '{cell_text}'")

                        # Extract data from cells - search for earnings in all columns
                        try:
                            # Extract clicks and fans from first numeric columns
                            numeric_values = []
                            for i, cell in enumerate(cells[1:], 1):  # Skip name column
                                cell_text = cell.text.strip()
                                # Skip percentage values and earnings (dollar signs)
                                if '%' not in cell_text and '$' not in cell_text:
                                    # Look for integer values (clicks, fans)
                                    numbers = re.findall(r'\d+', cell_text)
                                    if numbers:
                                        numeric_values.append(int(numbers[0]))

                            # Assign first two numeric values to clicks and fans
                            if len(numeric_values) >= 1:
                                clicks = numeric_values[0]
                            if len(numeric_values) >= 2:
                                fans = numeric_values[1]

                            # Extract earnings from the last column (rightmost column)
                            # Based on the screenshot, earnings appear in the rightmost column
                            earnings_found = False
                            for i in range(len(cells) - 1, 0, -1):  # Search from right to left, skip name column
                                earnings_text = cells[i].text.strip()
                                # print(f"DEBUG - Column {i} text: '{earnings_text}'")

                                # Look for dollar amounts like $630.40, $283.11, $2,361.50, etc.
                                earnings_match = re.search(r'\$\s*[\d,]+(?:\.\d+)?', earnings_text)
                                if earnings_match:
                                    try:
                                        # Remove $ and commas and spaces, convert to float
                                        earnings_str = earnings_match.group().replace('$', '').replace(',', '').replace(' ', '')
                                        earnings = float(earnings_str)
                                        # print(f"DEBUG - Found earnings ${earnings:.2f} in column {i}")
                                        earnings_found = True
                                        break
                                    except ValueError as e:
                                        # print(f"DEBUG - Error parsing earnings: {e}")
                                        continue

                            if not earnings_found:
                                print(f"DEBUG - No earnings found in any column")
                        except (IndexError, ValueError) as e:
                            print(f"Error extracting data from row: {e}")
                            continue

                        tracking_data.append((name, clicks, fans, earnings))
                        print(f"Extracted: {name} - Clicks: {clicks}, Fans: {fans}, Earnings: ${earnings:.2f}")

                except Exception as e:
                    print(f"Error processing row: {e}")
                    continue

            print(f"Successfully extracted {len(tracking_data)} tracking links")

            # save to csv file in debug folder

            with open("debug/onlymonster_tracking_data.csv", "w", newline="") as f:
                writer = csv.writer(f)
                writer.writerow(["Name", "Clicks", "Fans", "Earnings"])
                writer.writerows(tracking_data)

            # Debug: Print all extracted link names to verify we got everything
            print("\nExtracted tracking links:")
            for name, clicks, fans, earnings in tracking_data:
                print(f"  - {name}")

            # Check if we got all priority links from config
            from config import PRIORITY_LINKS
            extracted_names = [name for name, _, _, _ in tracking_data]
            missing_links = []

            for priority_link in PRIORITY_LINKS:
                # Check if priority link exists in extracted data (case-insensitive partial match)
                found = False
                for extracted_name in extracted_names:
                    if priority_link.lower() in extracted_name.lower() or extracted_name.lower() in priority_link.lower():
                        found = True
                        break
                if not found:
                    missing_links.append(priority_link)

            if missing_links:
                print(f"\n⚠️  WARNING: Missing {len(missing_links)} priority links:")
                for link in missing_links:
                    print(f"  - {link}")
                print("These links may need to be loaded with additional 'Load More' clicks or may have different names.")
            else:
                print("\n✅ All priority links found!")

            return tracking_data

        except Exception as e:
            print(f"Error extracting tracking data: {e}")
            return tracking_data

    def extract_dashboard_revenue_metrics(self, sb):
        """Extract total revenue, sales revenue, subscription revenue, and ARPPU from OnlyMonster dashboard."""
        try:
            # Open dashboard
            print(f"\n🔍 Extracting dashboard revenue metrics...")
            if not sb.get_current_url().endswith("panel/dashboard"):
                sb.uc_open_with_reconnect(DASHBOARD_URL, 3)
                self.__sleep_while_loading(sb)

            # Attempt multiple selector strategies since UI may change
            selectors = [
                ("Total Revenue", "Sales Revenue", "Subscription Revenue", "ARPPU"),
                ("Total revenue", "Sales revenue", "Subscription revenue", "ARPPU"),
                ("Revenue", "Sales", "Subscriptions", "ARPPU"),
            ]

            # Read all text once for fallback parse
            page_text = sb.find_element('body').text

            def parse_currency(val: str) -> float:
                m = re.search(r"\$\s*([\d,]+(?:\.\d+)?)", val)
                return float(m.group(1).replace(',', '')) if m else 0.0

            total = sales = subs = arppu = 0.0

            # Try semantic label-value pairs
            for labels in selectors:
                for label in labels:
                    try:
                        el = sb.find_element(f"//*[contains(text(), '{label}')]")
                        # Look for a nearby element with a $ or number
                        container = el.find_element('./ancestor::*[1]')
                        candidate = None
                        for xp in [
                            ".//following::*[1]",
                            "./following-sibling::*[1]",
                            ".//*[contains(text(), '$')][1]",
                        ]:
                            try:
                                candidate = container.find_element(xp)
                                if candidate and candidate.text.strip():
                                    break
                            except Exception:
                                pass
                        text = (candidate.text if candidate else '').strip()
                        if 'ARPPU' in label.upper():
                            arppu = parse_currency(text) if '$' in text else float(text.replace(',', '') or 0)
                        elif 'Total' in label or label.lower().startswith('revenue'):
                            total = parse_currency(text)
                        elif 'Sales' in label:
                            sales = parse_currency(text)
                        elif 'Subscription' in label or 'Subscriptions' in label:
                            subs = parse_currency(text)
                    except Exception:
                        continue

            # Fallback: global regex extraction from page text
            patterns = {
                'total': r"Total\s+Revenue\s*\$\s*([\d,]+(?:\.\d+)?)",
                'sales': r"Sales\s+Revenue\s*\$\s*([\d,]+(?:\.\d+)?)",
                'subs': r"Subscription\s+Revenue\s*\$\s*([\d,]+(?:\.\d+)?)",
                'arppu': r"ARPPU\s*\$\s*([\d,]+(?:\.\d+)?)",
            }
            if total == 0:
                print("Total revenue not found in page text")
                m = re.search(patterns['total'], page_text, flags=re.I)
                if m: total = float(m.group(1).replace(',', ''))
            if sales == 0:
                print("Sales revenue not found in page text")
                m = re.search(patterns['sales'], page_text, flags=re.I)
                if m: sales = float(m.group(1).replace(',', ''))
            if subs == 0:
                print("Subscription revenue not found in page text")
                m = re.search(patterns['subs'], page_text, flags=re.I)
                if m: subs = float(m.group(1).replace(',', ''))
            if arppu == 0:
                print("ARPPU not found in page text")
                m = re.search(patterns['arppu'], page_text, flags=re.I)
                if m: arppu = float(m.group(1).replace(',', ''))

            # If we got any non-zero values, return them
            if any([total, sales, subs, arppu]):
                print(f"Extracted dashboard metrics: Total={total}, Sales={sales}, Subs={subs}, ARPPU={arppu}")
                return (total, sales, subs, arppu)
            return None
        except Exception as e:
            print(f"Error extracting dashboard metrics: {e}")
            return None

    def run_scraping(self):
        """Main method to run the complete scraping process"""

        # Use SeleniumBase context manager with optimal settings
        with SB(
            uc=True,               # Use undetected-chromedriver (anti-detection)
            headless=HEADLESS,     # Respect configuration for headless
            # incognito=True,        # Use incognito mode for clean sessions
            ad_block_on=False,     # Keep ads for realistic browsing
            maximize=True,         # Maximize window for better element detection
        ) as sb:
            try:
                print("Starting OnlyMonster scraping...")
                # Try cookies first; if they fail, fall back to interactive Clerk login
                if not self.try_cookie_login(sb):
                    # In headless/CI environments, interactive login is brittle and often blocked.
                    # Prefer asking the operator to seed cookies via scripts/seed_onlymonster_cookies.py
                    if HEADLESS:
                        raise Exception(
                            "Cookie login failed in headless mode. Seed session cookies by running: "
                            "HEADLESS=false PYTHONPATH=. python3 scripts/seed_onlymonster_cookies.py"
                        )
                    else:
                        print("Cookie login failed; attempting interactive login…")
                        # open new tab
                        # clear all cookies
                        self.login(sb)
                        # After successful login, persist cookies for future runs
                        try:
                            self.save_cookies(sb)
                        except Exception:
                            pass

                # Also scrape dashboard revenue metrics before tracking links
                try:
                    dash_metrics = self.extract_dashboard_revenue_metrics(sb)
                    if dash_metrics:
                        tr, sr, subr, arppu = dash_metrics
                        self.db.insert_om_dashboard_metrics(tr, sr, subr, arppu)
                        print(f"Stored dashboard metrics: total={tr}, sales={sr}, subs={subr}, arppu={arppu}")
                except Exception as e:
                    print(f"Dashboard metrics scraping failed (non-fatal): {e}")

                # Navigate to tracking links


                # Extract data
                tracking_data = self.extract_tracking_data(sb)

                if tracking_data:
                    # Store in database
                    self.db.insert_tracking_data(tracking_data)
                    print(f"Successfully stored {len(tracking_data)} records in database")
                else:
                    print("No tracking data found to store")

                return tracking_data

            except Exception as e:
                print(f"Scraping failed: {e}")
                raise


if __name__ == "__main__":
    scraper = OnlyMonsterScraper()
    try:
        data = scraper.run_scraping()
        print("Scraping completed successfully!")

        # Run analytics after successful scraping
        if data:
            print("\n" + "="*60)
            print("🔍 RUNNING ANALYTICS...")
            print("="*60)

            analytics = TrackingAnalytics()
            analysis = analytics.run_analysis(24)  # Compare with 24 hours ago
            analytics.print_analysis_report(analysis)
        else:
            print("No data collected, skipping analysis.")

    except Exception as e:
        print(f"Scraping failed: {e}")
