# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Environment Setup
```bash
# Activate virtual environment (required for all operations)
source venv/bin/activate

# Install dependencies 
pip install -r requirements.txt
```

### Core Operations

#### Testing
```bash
# Run all tests
for test in tests/test_*.py; do python "$test"; done

# Run specific test categories
python tests/test_database.py           # Database functionality
python tests/test_scraper_init.py       # Scraper initialization  
python tests/test_earnings_tracking.py  # Earnings system
python tests/test_earnings_extraction.py # Earnings parsing

# Debug and preview tools
python tests/debug_slack.py            # Slack integration debugging
python tests/preview_standardized_slack.py # Preview report format
```

#### Data Collection & Analysis
```bash
# Single scraping session
python onlymonster_scraper.py

# Scrape with immediate analysis
python scrape_and_analyze.py

# Generate daily report with Slack notification
python standardized_daily_report.py

# Generate weekly report
python weekly_report.py

# Run analytics only (no scraping)
python run_analytics.py [hours_back]
```

#### Automation Management
```bash
# Setup daily automation (SystemD timer - recommended for servers)
./setup_daily_automation.sh

# Setup weekly automation  
sudo bash setup_weekly_automation.sh

# Check automation status
systemctl status onlymonster-daily.timer
systemctl status onlymonster-weekly.timer

# View automation logs
journalctl -u onlymonster-daily.service -f
journalctl -u onlymonster-weekly.service -f
```

### Data Management
```bash
# Add historical data
python add_historical_data.py

# Fix data ordering issues
python fix_data_ordering.py

# Fix earnings data 
python fix_earnings_data.py
```

## Architecture Overview

### Core Components

#### Data Layer
- **`database.py`**: SQLite database operations with `tracking_data` table containing columns: `id`, `tracking_link_name`, `clicks`, `fans`, `earnings`, `timestamp`, `created_at`
- **`config.py`**: Configuration management including credentials, API keys, and priority link definitions
- **Link combining system**: `link_combiner.py` allows combining multiple tracking links for unified analytics (e.g., combining multiple Reddit links)

#### Web Scraping Engine  
- **`onlymonster_scraper.py`**: Selenium-based web scraper that logs into OnlyMonster.ai and extracts tracking data
- **Headless Chrome** with anti-detection measures (realistic user agents, disabled automation flags)
- **Robust error handling** with page source saving for debugging failed scrapes
- **Earnings extraction** from OnlyMonster platform interface

#### Analytics & AI Integration
- **`analytics.py`**: AI-powered analytics using Claude Sonnet 4 via OpenRouter API
- **Priority link analysis** for focused reporting on key platforms
- **Growth calculations** and trend analysis with historical comparisons  
- **Conversion rate analysis** (fans to earnings ratios)
- **Strategic recommendations** generated by AI for optimization

#### Reporting & Notifications
- **`standardized_daily_report.py`**: Main daily reporting script with AI-generated insights
- **`weekly_report.py`**: Comprehensive weekly performance analysis
- **`slack_webhook.py`**: Slack integration for automated notifications
- **Message formatting** with performance indicators and growth metrics

### Automation Architecture

#### SystemD Integration (Preferred)
- **`onlymonster-daily.service`** & **`onlymonster-daily.timer`**: Daily automation that survives SSH disconnections
- **`onlymonster-weekly.service`** & **`onlymonster-weekly.timer`**: Weekly reporting automation
- **Boot-time initialization** and automatic restart on failures
- **Comprehensive logging** via journalctl

#### Scheduling Components
- **`daily_scheduler.py`**: Python-based daily scheduler  
- **`weekly_scheduler.py`**: Python-based weekly scheduler
- **Setup scripts**: `setup_daily_automation.sh`, `setup_weekly_automation.sh` for easy deployment

### Data Flow
1. **Scraper** logs into OnlyMonster.ai and extracts tracking data (clicks, fans, earnings)
2. **Database** stores timestamped records with automatic schema migration
3. **Link Combiner** merges specified tracking links for unified analysis  
4. **Analytics** processes data and generates AI-powered insights via OpenRouter
5. **Reports** format findings and send to Slack with performance indicators

### Configuration Requirements
- OnlyMonster.ai credentials (`EMAIL`, `PASSWORD`)
- OpenRouter API key for Claude Sonnet 4 analysis  
- Slack bot token and channel ID for notifications
- Priority links list for focused analysis
- Combined links mapping for unified reporting

### Key Design Patterns
- **Database-first approach**: All scraped data persists in SQLite before analysis
- **Modular analytics**: Separate classes for database, analytics, and notifications
- **Error resilience**: Comprehensive exception handling with Slack error notifications  
- **AI integration**: Strategic use of Claude Sonnet 4 for insights rather than basic data processing
- **Server-optimized**: Headless operation with SystemD integration for production deployment

## Task Management System

### Task Workflow
When working with this codebase, use the integrated task management system:

1. **View Tasks**: `./view-tasks.sh` or read `/root/onlymonster-automations/TASKS.md`
2. **Add Tasks**: Edit `TASKS.md` and add tasks under "## Pending Tasks" section
3. **Complete Tasks**: Tell Claude "complete the tasks" to start automated workflow

### Task Completion Workflow
When "complete the tasks" is requested:
1. Parse all pending tasks from `TASKS.md`
2. Work through each task systematically using TodoWrite tool
3. **After each task completion**: 
   - Mark task as completed in `TASKS.md`
   - Rebuild and restart the webapp Docker container
   - Verify cheeksdash.com is serving updated version
   - Show live verification before proceeding to next task
4. Move completed tasks to "Completed Tasks" section with timestamps

### Webapp Refresh Process
The system automatically refreshes the live webapp after each task:
- Runs `docker compose build --no-cache cheeksdash`
- Executes `docker compose up -d cheeksdash` 
- Waits for container to be ready
- Verifies cheeksdash.com responds with HTTP 200
- Confirms changes are live before proceeding

### Task Format Examples
```markdown
## Pending Tasks

### Dashboard Improvements
- [ ] Make social media cards more compact on mobile
- [ ] Add CSV export functionality for metrics
- [ ] **Priority**: Fix header alignment on tablet devices

### New Features  
- [ ] **Dark Mode**: Implement theme toggle in dashboard header
  - Add toggle switch to header actions
  - Create CSS variables for dark theme
  - Store theme preference in localStorage
```

**Important**: Always use this task system for complex multi-step work to ensure proper progress tracking and webapp deployment verification.