Deployment and Operations

Overview

- Single database file used across collectors, reports, and dashboard.
- Default DB path: onlymonster_data.db in the repo root.
- Override with env var DATABASE_PATH when needed (e.g., container path /app/data/onlymonster_data.db).

Database Path

- Local runs: core/config.py resolves to an absolute path at repo_root/onlymonster_data.db, regardless of current working dir.
- Dashboard container: web_dashboard/docker-compose.yml sets DATABASE_PATH to /app/data/onlymonster_data.db and mounts the host repo to /app/data.
- Configure the host mount path via HOST_DATA_DIR env var when running docker-compose.

Example docker-compose usage

- cd web_dashboard
- export HOST_DATA_DIR=/absolute/path/to/onlymonster-automations
- docker compose up -d --build

Verify DB connectivity (dashboard)

- Visit https://cheeksdash.com/api/debug/database
- Response includes: database_path, database_exists, total_records, and top_links

Ingestion and Scheduling

- Social (TikTok, Instagram, Reddit): store_social_data_optimized.py
  - Designed for multiple runs per day without DB bloat
  - Recommended systemd timer: setup_smart_social_monitoring.sh
    - 4x daily: 09:00, 13:00, 18:00, 22:00
    - Daily alternative at 06:00 also provided

- OnlyMonster tracking links (clicks, fans, earnings): core/onlymonster_scraper.py
  - Heavier Selenium automation
  - Recommended systemd timer: setup_onlymonster_scheduling.sh
    - 2x daily: 07:00 and 19:00

- Integrated option (both + cleanup): store_integrated_data.py

Manual runs

- Social only: python3 store_social_data_optimized.py
- Integrated: python3 store_integrated_data.py

Notes

- The scheduler automation/daily_scheduler.py now executes the optimized storage script from this repo and logs to daily_scheduler.log in the repo root.
- If the dashboard shows no data, confirm:
  - The mounted host path matches your repo location (HOST_DATA_DIR)
  - The database file exists and updates are occurring (check timestamps)
  - /api/debug/database shows database_exists=true and a nonzero total_records
