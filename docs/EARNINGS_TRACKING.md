# 💰 OnlyMonster Earnings Tracking

This document describes the earnings tracking functionality that automatically captures earnings data from OnlyMonster's tracking links page.

## Overview

The earnings tracking system:
- **Automatically scrapes earnings** from OnlyMonster's "Earnings" column
- **Stores earnings data** alongside clicks and fans in the database
- **Provides analytics** on earnings per fan performance
- **Identifies zero-earnings platforms** for optimization
- **Includes earnings insights** in AI analysis and Slack reports

## Key Features

### 1. Automatic Earnings Capture
- Scraper extracts earnings data from OnlyMonster's tracking links page
- Earnings are stored in the database with each scraping run
- No manual input required - fully automated

### 2. Earnings Analytics
- **Earnings per fan calculations** - identify most profitable platforms
- **Zero earnings detection** - flag platforms bringing fans but no revenue
- **Earnings growth tracking** - monitor earnings changes over time
- **Platform performance comparison** - rank platforms by profitability

### 3. AI-Powered Insights
The AI analysis now focuses on:
- Which platforms generate highest earnings per fan
- Which platforms have fans but zero earnings (optimization opportunities)
- Earnings efficiency and ROI analysis
- Strategic recommendations for earnings optimization

## Database Schema

The `tracking_data` table includes:
```sql
CREATE TABLE tracking_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tracking_link_name TEXT NOT NULL,
    clicks INTEGER NOT NULL,
    fans INTEGER NOT NULL,
    earnings DECIMAL(10,2) DEFAULT 0.00,  -- From OnlyMonster platform
    revenue DECIMAL(10,2) DEFAULT 0.00,   -- Manual tracking (optional)
    timestamp DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Analytics Output

### Text Reports Include:
1. **Top Earnings Sources** - Platforms generating most earnings
2. **Highest Earnings Per Fan** - Most profitable platforms per fan acquired
3. **Zero Earnings Platforms** - Platforms with fans but no earnings
4. **Earnings Growth** - Changes in earnings over time

### Example Output:
```
💰 TOP EARNINGS SOURCES (OnlyMonster Platform):
   reels-naominoface: +$125.50
   tiktok-aug-1-24: +$89.75
   chive-nyla-aug-8: +$67.25

💎 HIGHEST EARNINGS PER FAN:
   reels-naominoface: $2.51/fan
   tiktok-aug-1-24: $1.87/fan
   chive-nyla-aug-8: $1.45/fan

⚠️  ZERO EARNINGS PLATFORMS:
   reddit-babycheeksx: 45 fans but $0.00 earnings
   chive-aug-1-24: 23 fans but $0.00 earnings
```

## AI Analysis Focus

The AI now provides insights on:
- **Monetization gaps** - platforms with high fan counts but low earnings
- **ROI optimization** - which platforms provide best return per fan
- **Strategic recommendations** - focus on earnings-generating platforms
- **Performance benchmarking** - compare earnings efficiency across platforms

## Integration with Existing Workflows

### Scraping
- Earnings are automatically captured during regular scraping
- No changes needed to existing scraping schedules
- Backward compatible with existing data

### Daily Reports
- Earnings metrics included in all Slack reports
- AI analysis focuses on earnings performance
- Zero-earnings platforms highlighted for attention

### Analytics
- All existing analytics continue to work
- New earnings-focused metrics added
- Earnings data included in AI prompts

## Usage

### Run Scraper (captures earnings automatically):
```bash
python onlymonster_scraper.py
```

### Test Earnings Functionality:
```bash
python test_earnings_tracking.py
```

### Generate Analytics with Earnings:
```bash
python run_analytics.py
```

### Daily Reports with Earnings:
```bash
python standardized_daily_report.py
```

## Key Benefits

1. **Automatic Data Collection** - No manual entry required
2. **Performance Insights** - Identify most/least profitable platforms
3. **Optimization Opportunities** - Flag zero-earnings platforms
4. **ROI Analysis** - Understand earnings efficiency per fan
5. **Strategic Focus** - AI recommendations for earnings growth

## Troubleshooting

### Earnings Not Captured
- Verify OnlyMonster page structure hasn't changed
- Check that "Earnings" column is visible on tracking links page
- Review scraper logs for extraction errors

### Zero Earnings Showing
- This is expected for new or underperforming platforms
- Use insights to optimize these platforms
- Focus marketing efforts on high-earning platforms

## Future Enhancements

Potential improvements:
- Earnings forecasting and predictions
- Platform-specific earnings optimization suggestions
- Automated alerts for earnings drops
- Historical earnings trend analysis

## Summary

The earnings tracking system provides automated capture and analysis of OnlyMonster platform earnings, enabling data-driven decisions about which tracking links and platforms are most profitable. The AI analysis focuses on earnings optimization and identifying monetization opportunities.

Key metrics tracked:
- **Total earnings** per platform
- **Earnings per fan** efficiency
- **Zero earnings platforms** for optimization
- **Earnings growth** over time

This enables strategic focus on the most profitable platforms and optimization of underperforming ones.
