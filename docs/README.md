# 🚀 OnlyMonster.ai Automation Suite

A comprehensive automation system for OnlyMonster.ai tracking links that provides automated data collection, AI-powered analytics, and intelligent Slack reporting with earnings tracking capabilities.

Dashboard location: cheeksxdash.com

This is to aid in social media monitoring and analytics for OF promotion. 

## 🎯 Overview

This automation suite logs into OnlyMonster.ai, extracts tracking data (clicks, fans, earnings), stores it in a SQLite database, and provides AI-powered insights through automated Slack reports. The system is designed for continuous monitoring of social media platform performance with revenue optimization recommendations.

## ✨ Key Features

### 🔄 Automated Data Collection
- **Automated login** to OnlyMonster.ai with robust error handling
- **Smart navigation** to tracking links page with retry mechanisms
- **Comprehensive data extraction**: link names, clicks, fans, and earnings
- **Reliable data storage** in SQLite database with timestamps
- **Configurable browser modes** (headless/visible) for debugging

### 💰 Advanced Earnings Tracking
- **Real-time earnings capture** from OnlyMonster platform
- **Earnings per fan analysis** to identify most profitable platforms
- **Zero-earnings detection** for optimization opportunities
- **Revenue growth tracking** with historical comparisons
- **Platform profitability rankings** for strategic decision making

### 🤖 AI-Powered Analytics
- **Claude Sonnet 4 integration** via OpenRouter API for advanced reasoning
- **Intelligent performance analysis** with actionable recommendations
- **Conversion rate insights** and optimization strategies
- **Automated trend detection** and anomaly identification
- **Strategic scaling suggestions** for successful campaigns

### 📱 Smart Slack Integration
- **Automated daily reports** with customizable scheduling
- **AI-generated insights** included in all notifications
- **Real-time performance alerts** for significant changes
- **Formatted reports** with clear metrics and visual indicators
- **Error notifications** for system monitoring

### 🛠️ System Automation
- **SystemD timer integration** for reliable scheduling
- **SSH-safe operation** that continues after disconnection
- **Automatic restart** on system failures
- **Boot-time initialization** for server environments
- **Comprehensive logging** and error handling

## 🚀 Quick Start

### Prerequisites
- Python 3.8+ installed
- Chrome browser installed
- OnlyMonster.ai account credentials
- OpenRouter API key (for AI analysis)
- Slack workspace with bot permissions

### 1. Environment Setup
```bash
# Clone or navigate to the project directory
cd onlymonster-automations

# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration
Create and configure your `config.py` file with the following settings:

```python
# OnlyMonster.ai Credentials
ONLYMONSTER_USERNAME = "your-username"
ONLYMONSTER_PASSWORD = "your-password"

# AI Analysis (Claude Sonnet 4)
OPENROUTER_API_KEY = "your-openrouter-api-key"

# Slack Integration
SLACK_BOT_TOKEN = "xoxb-your-slack-bot-token"
SLACK_CHANNEL_ID = "your-channel-id"

# Browser Settings
HEADLESS = True  # Set to False for debugging
TIMEOUT = 30
```

### 3. Initial Setup & Testing
```bash
# Test database functionality
python tests/test_database.py

# Test scraper initialization
python tests/test_scraper_init.py

# Test Slack integration
python tests/debug_slack.py

# Run first scrape
python onlymonster_scraper.py
```

### 4. Automated Daily Reports
```bash
# Set up automated daily reports (recommended)
./setup_daily_automation.sh

# Or use simple cron setup
./setup_cron_only.sh
```

## 📖 Usage Guide

### Core Operations

#### Data Collection
```bash
# Run single scraping session
python onlymonster_scraper.py

# Scrape with immediate analysis
python scrape_and_analyze.py

# Enhanced scraping with advanced analytics
python scrape_and_analyze_enhanced.py
```

#### Analytics & Reporting

##### Daily Reports
```bash
# Generate daily report with AI insights
python daily_report_with_slack.py

# Standardized daily report format
python standardized_daily_report.py

# Run analytics only (no Slack notification)
python run_analytics.py [hours_back]

# Preview Slack message format
python tests/preview_standardized_slack.py
```

##### Weekly Reports
```bash
# Generate comprehensive weekly report
python weekly_report.py

# Test weekly report functionality
python tests/test_weekly_report.py

# Set up weekly automation (run as root)
sudo bash setup_weekly_automation.sh
```

#### Automation Management

##### Daily Automation
```bash
# Start automated daily scheduler
python daily_scheduler.py

# Check daily automation status
systemctl status onlymonster-daily.timer
systemctl status onlymonster-daily.service

# View daily automation logs
journalctl -u onlymonster-daily.service -f
```

##### Weekly Automation
```bash
# Start automated weekly scheduler
python weekly_scheduler.py

# Check weekly automation status
systemctl status onlymonster-weekly.timer
systemctl status onlymonster-weekly.service

# View weekly automation logs
journalctl -u onlymonster-weekly.service -f
```

### Data Access & Analysis

#### Database Queries
```python
from database import TrackingDatabase
from analytics import TrackingAnalytics

# Initialize database connection
db = TrackingDatabase()

# Get recent data
recent_data = db.get_latest_data(10)
for name, clicks, fans, earnings, timestamp in recent_data:
    print(f"{name}: {clicks} clicks, {fans} fans, ${earnings:.2f} ({timestamp})")

# Get data for specific date range
date_data = db.get_data_by_date_range("2024-01-01", "2024-01-31")

# Analytics with AI insights
analytics = TrackingAnalytics()
insights = analytics.analyze_performance(hours_back=24)
```

#### Historical Data Management
```bash
# Add historical data from CSV/manual entry
python add_historical_data.py

# Insert historical data with proper ordering
python insert_historical_data.py

# Fix data ordering issues
python fix_data_ordering.py
```

## 🗄️ Database Schema

The SQLite database (`onlymonster_data.db`) contains the following structure:

### `tracking_data` Table
| Column | Type | Description |
|--------|------|-------------|
| `id` | INTEGER PRIMARY KEY | Auto-incrementing unique identifier |
| `tracking_link_name` | TEXT | Name of the tracking link/platform |
| `clicks` | INTEGER | Total number of clicks |
| `fans` | INTEGER | Total number of fans/subscribers |
| `earnings` | REAL | Earnings from OnlyMonster platform |
| `timestamp` | TEXT | When the data was scraped (ISO format) |
| `created_at` | TEXT | Database record creation time |

### Data Relationships
- Each record represents a snapshot of a tracking link's performance at a specific time
- Historical data is maintained for trend analysis and growth calculations
- Earnings data is captured automatically from OnlyMonster's interface

## 🤖 AI Analytics & Slack Integration

### AI-Powered Analysis Engine
- **Model**: Claude Sonnet 4 (anthropic/claude-3.7-sonnet:thinking) via OpenRouter API
- **Capabilities**:
  - Advanced reasoning and pattern recognition
  - Performance trend analysis and forecasting
  - Conversion rate optimization insights
  - Strategic scaling recommendations
  - Anomaly detection and alert generation

### Intelligent Slack Reporting
- **Automated daily reports** with customizable scheduling
- **AI-generated insights** included in every notification
- **Performance alerts** for significant changes or opportunities
- **Formatted reports** with clear metrics and visual indicators
- **Error notifications** for system monitoring and maintenance

### Report Types
1. **Daily Performance Summary**: Overview of all platforms with growth metrics
2. **Earnings Analysis**: Revenue performance and profitability insights
3. **Conversion Rate Updates**: Fan-to-revenue conversion tracking
4. **Strategic Recommendations**: AI-powered optimization suggestions
5. **System Status**: Automation health and error reporting

## 📁 Project Structure

### Core Scripts
- `onlymonster_scraper.py` - Main web scraping engine
- `analytics.py` - AI-powered analytics and insights generation
- `database.py` - Database operations and data management
- `config.py` - Configuration settings and credentials
- `slack_webhook.py` - Slack integration and message formatting

### Automation & Scheduling
- `daily_scheduler.py` - Python-based scheduling system
- `daily_report_with_slack.py` - Automated daily reporting
- `standardized_daily_report.py` - Standardized report format
- `scrape_and_analyze.py` - Combined scraping and analysis
- `scrape_and_analyze_enhanced.py` - Advanced scraping with extra features

### Data Management
- `add_historical_data.py` - Historical data import utilities
- `insert_historical_data.py` - Bulk historical data insertion
- `fix_data_ordering.py` - Data ordering and cleanup utilities
- `fix_earnings_data.py` - Earnings data correction tools

### System Setup
- `setup_daily_automation.sh` - SystemD timer setup script
- `setup_cron_only.sh` - Simple cron-based setup
- `manage_automation.sh` - Automation management utilities
- `onlymonster-daily.service` - SystemD service configuration
- `onlymonster-daily.timer` - SystemD timer configuration

### Testing & Debugging
- `tests/` - Complete test suite (see [tests/README.md](tests/README.md))
- `tests/test_*.py` - Unit and integration tests
- `tests/debug_slack.py` - Slack integration debugging
- `tests/preview_*.py` - Message format preview tools

### Documentation
- `README.md` - Main project documentation (this file)
- `AUTOMATION_SETUP.md` - Detailed automation setup guide
- `EARNINGS_TRACKING.md` - Earnings tracking system documentation
- `tests/README.md` - Testing framework documentation

## 🔧 Testing & Quality Assurance

### Test Suite
The project includes a comprehensive test suite located in the `tests/` directory:

```bash
# Run all tests
for test in tests/test_*.py; do python "$test"; done

# Run specific test categories
python tests/test_database.py          # Database functionality
python tests/test_scraper_init.py      # Scraper initialization
python tests/test_earnings_tracking.py # Earnings system
python tests/test_earnings_extraction.py # Earnings parsing

# Debug and preview tools
python tests/debug_slack.py           # Slack integration debugging
python tests/preview_slack_message.py # Message format preview
```

For detailed testing information, see [tests/README.md](tests/README.md).

## 🚨 Troubleshooting

### Common Issues & Solutions

#### Authentication Problems
```bash
# Check credentials
python tests/test_scraper_init.py

# Debug login process
HEADLESS=False python onlymonster_scraper.py
```
- Verify credentials in `config.py`
- Check for CAPTCHA or 2FA requirements
- Ensure OnlyMonster.ai site structure hasn't changed

#### Data Extraction Issues
```bash
# Check page structure
python onlymonster_scraper.py  # Creates page_source.html for debugging
```
- Review saved `page_source.html` file
- Update CSS selectors if page structure changed
- Check for dynamic content loading issues

#### Slack Integration Problems
```bash
# Test Slack connectivity
python tests/debug_slack.py

# Verify bot permissions
# Check channel access and token validity
```
- Verify bot token and channel ID in `config.py`
- Ensure bot has proper permissions in Slack workspace
- Check network connectivity and firewall settings

#### Browser & WebDriver Issues
```bash
# Test browser setup
python tests/test_scraper_init.py

# Run in visible mode for debugging
HEADLESS=False python onlymonster_scraper.py
```
- Update Chrome browser to latest version
- Verify ChromeDriver compatibility
- Check system PATH and permissions

#### Database Problems
```bash
# Test database functionality
python tests/test_database.py

# Check database file permissions
ls -la onlymonster_data.db
```
- Verify database file exists and is writable
- Check SQLite installation and version
- Review database schema and data integrity

### System Monitoring

#### Automation Status
```bash
# Check SystemD timer status
systemctl status onlymonster-daily.timer
systemctl status onlymonster-daily.service

# View recent logs
journalctl -u onlymonster-daily.service -n 50

# Monitor real-time logs
journalctl -u onlymonster-daily.service -f
```

#### Performance Monitoring
```bash
# Check disk space
df -h

# Monitor memory usage
free -h

# Check process status
ps aux | grep python
```

## 🔒 Security & Best Practices

### Credential Management
- **Development**: Store credentials in `config.py` (excluded from version control)
- **Production**: Use environment variables or secure credential management
- **Backup**: Regularly backup database and configuration files

### Rate Limiting & Compliance
- Respect OnlyMonster.ai terms of service
- Implement reasonable delays between requests
- Monitor for rate limiting responses
- Use appropriate user agents and headers

### Data Protection
- Regularly backup `onlymonster_data.db`
- Implement data retention policies
- Secure sensitive analytics data
- Monitor access logs and usage patterns

## 📚 Additional Resources

### Documentation
- [Automation Setup Guide](AUTOMATION_SETUP.md) - Detailed setup instructions
- [Earnings Tracking Guide](EARNINGS_TRACKING.md) - Earnings system documentation
- [Test Suite Documentation](tests/README.md) - Testing framework guide

### External Dependencies
- [Selenium Documentation](https://selenium-python.readthedocs.io/)
- [OpenRouter API Documentation](https://openrouter.ai/docs)
- [Slack API Documentation](https://api.slack.com/)
- [SQLite Documentation](https://sqlite.org/docs.html)

### Support & Maintenance
- Monitor system logs regularly
- Update dependencies periodically
- Test functionality after OnlyMonster.ai updates
- Maintain backup and recovery procedures
