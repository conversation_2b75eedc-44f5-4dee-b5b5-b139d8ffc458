#!/usr/bin/env bash
set -euo pipefail

# Enable and start systemd timers for social and OnlyMonster collectors.
# Run as root.

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPO_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$REPO_ROOT"

echo "[timers] Setting up smart social monitoring timer"
sudo bash ./setup_smart_social_monitoring.sh
sudo systemctl enable --now social-media-smart.timer

echo "[timers] Setting up OnlyMonster scraper timer"
sudo bash ./setup_onlymonster_scheduling.sh
sudo systemctl enable --now onlymonster-scraper.timer

echo "[timers] Active timers:"
systemctl list-timers | grep -E 'social-media|onlymonster' || true

echo "[timers] Tail logs (Ctrl+C to exit):"
echo "  sudo journalctl -u social-media-smart.service -f"
echo "  sudo journalctl -u onlymonster-scraper.service -f"

