#!/usr/bin/env python3
"""
Run a fresh scrape and immediate analysis with retry logic
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.onlymonster_scraper import OnlyMonsterScraper
from core.analytics import TrackingAnalytics
from reports.standardized_daily_report import generate_standardized_report, format_slack_message
from slack.slack_webhook import SlackWebhookNotifier
from core.config import SLACK_BOT_TOKEN, SLACK_CHANNEL_ID

def main():
    print("🚀 Starting OnlyMonster Scrape & Analysis")
    print("="*60)
    
    max_attempts = 3
    data = None
    
    # Try scraping with retry logic
    for attempt in range(1, max_attempts + 1):
        print(f"\n🔄 Scraping attempt {attempt}/{max_attempts}")
        print("-" * 40)
        
        scraper = OnlyMonsterScraper()
        try:
            data = scraper.run_scraping()
            
            if data:
                print(f"\n✅ Successfully scraped {len(data)} tracking links on attempt {attempt}")
                break  # Success, exit retry loop
            else:
                print(f"❌ No data collected on attempt {attempt}")
                if attempt < max_attempts:
                    print(f"🔄 Retrying... ({max_attempts - attempt} attempts remaining)")
                    continue
                
        except Exception as e:
            print(f"❌ Scraping failed on attempt {attempt}: {e}")
            if attempt < max_attempts:
                print(f"🔄 Retrying... ({max_attempts - attempt} attempts remaining)")
                continue
            else:
                print(f"💥 All {max_attempts} scraping attempts failed")
                break
    
    # If we have data, proceed with analysis and Slack notification
    if data:
        # Run standardized analytics
        print("\n📊 Running Standardized Analysis...")
        report_data = generate_standardized_report()

        # Send to Slack
        print(f"\n📤 SENDING TO SLACK...")
        print("-" * 25)

        try:
            slack_notifier = SlackWebhookNotifier()
            message = format_slack_message(report_data)

            success = slack_notifier.send_message_via_api(message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)

            if success:
                print("✅ Fresh data + standardized report sent to Slack!")
                print(f"📱 Check: https://cheeksglobal.slack.com/archives/{SLACK_CHANNEL_ID}")
            else:
                print("❌ Failed to send to Slack")

        except Exception as e:
            print(f"❌ Slack error: {e}")
            print("📧 Report generated locally only")
    else:
        print("💥 Final result: No data collected after all attempts")
        
        # Send failure notification to Slack
        try:
            slack_notifier = SlackWebhookNotifier()
            failure_message = f"🚨 *OnlyMonster Scraping Failed*\n\nAll {max_attempts} scraping attempts failed. Please check the system."
            
            slack_notifier.send_message_via_api(failure_message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)
            print("📱 Failure notification sent to Slack")
        except Exception as e:
            print(f"❌ Failed to send failure notification to Slack: {e}")

if __name__ == "__main__":
    main()
