#!/usr/bin/env python3
"""
Standardized OnlyMonster Daily Report with Historical Analysis
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlite3
import json
from datetime import datetime, timed<PERSON>ta
from statistics import mean, stdev
from core.analytics import TrackingAnalytics
from slack.slack_webhook import SlackWebhookNotifier
from core.link_combiner import Link<PERSON><PERSON>iner
from core.config import DATABASE_PATH, PRIORITY_LINKS, SLACK_BOT_TOKEN, SLACK_CHANNEL_ID

def validate_data_ordering():
    """Check for potential data insertion order issues and warn if found"""
    with sqlite3.connect(DATABASE_PATH) as conn:
        cursor = conn.cursor()

        # Check if latest timestamp matches latest insertion
        cursor.execute('SELECT MAX(timestamp) FROM tracking_data')
        latest_timestamp = cursor.fetchone()[0]

        cursor.execute('SELECT timestamp FROM tracking_data ORDER BY id DESC LIMIT 1')
        latest_entry = cursor.fetchone()[0]

        if latest_timestamp != latest_entry:
            print("⚠️  WARNING: Data insertion order issue detected!")
            print(f"   Latest timestamp: {latest_timestamp}")
            print(f"   Latest entry: {latest_entry}")
            print("   This may affect report accuracy. Consider running data reordering.")
            return False
        return True

def get_date_range():
    """Get the actual comparison date range (matches the growth calculation logic)"""
    with sqlite3.connect(DATABASE_PATH) as conn:
        cursor = conn.cursor()

        # Get the latest timestamp (most recent data chronologically)
        cursor.execute('SELECT MAX(timestamp) FROM tracking_data')
        latest_timestamp = cursor.fetchone()[0]

        # Get the second most recent timestamp (what we actually compare against)
        # This matches the logic in calculate_growth_metrics()
        cursor.execute('''
            SELECT DISTINCT timestamp FROM tracking_data
            ORDER BY timestamp DESC
            LIMIT 2
        ''')
        timestamps = cursor.fetchall()

        if len(timestamps) >= 2:
            # Use the actual comparison timestamps
            latest_timestamp = timestamps[0][0]
            previous_timestamp = timestamps[1][0]
        else:
            # Fallback to 7-day window if we don't have enough data points
            cursor.execute('''
                SELECT MIN(timestamp) FROM tracking_data
                WHERE timestamp >= datetime(?, '-7 days')
            ''', (latest_timestamp,))
            previous_result = cursor.fetchone()
            previous_timestamp = previous_result[0] if previous_result[0] else latest_timestamp

        if not latest_timestamp or not previous_timestamp:
            return "No data", "No data", 0

        # Parse timestamps and format dates
        try:
            latest_date = datetime.fromisoformat(latest_timestamp.replace('Z', '+00:00'))
            previous_date = datetime.fromisoformat(previous_timestamp.replace('Z', '+00:00'))

            # Calculate days difference
            days_diff = (latest_date - previous_date).days
            if days_diff == 0:
                days_diff = 1  # At least 1 day for same-day comparisons

            # Format dates as MM/DD/YY
            latest_formatted = latest_date.strftime("%m/%d/%y")
            previous_formatted = previous_date.strftime("%m/%d/%y")

            return previous_formatted, latest_formatted, days_diff

        except Exception as e:
            print(f"Date parsing error: {e}")
            return "Unknown", "Unknown", 1

def get_historical_data():
    """Get all historical data for trend analysis with link combinations (robust ordering)"""
    link_combiner = LinkCombiner()

    with sqlite3.connect(DATABASE_PATH) as conn:
        cursor = conn.cursor()

        # Get all data ordered by timestamp (chronological, most recent first)
        # This ensures proper ordering regardless of insertion order
        cursor.execute('''
            SELECT tracking_link_name, clicks, fans, timestamp
            FROM tracking_data
            ORDER BY timestamp DESC
        ''')

        data_by_link = {}
        for name, clicks, fans, timestamp in cursor.fetchall():
            if name not in data_by_link:
                data_by_link[name] = []
            data_by_link[name].append({
                'clicks': clicks,
                'fans': fans,
                'timestamp': timestamp
            })

        # Apply link combinations
        combined_data = link_combiner.combine_historical_data(data_by_link)
        return combined_data

def calculate_growth_metrics(data_by_link):
    """Calculate growth metrics and deviations (robust against data insertion order)"""
    metrics = {}

    for link in PRIORITY_LINKS:
        if link in data_by_link and len(data_by_link[link]) >= 2:
            # Sort by timestamp (most recent first) - ensures chronological order
            # This protects against historical data inserted after current data
            sorted_data = sorted(data_by_link[link], key=lambda x: x['timestamp'], reverse=True)

            # Current vs previous (most recent vs second most recent chronologically)
            current = sorted_data[0]
            previous = sorted_data[1]

            # Validate that we're comparing reasonable time periods
            curr_time = datetime.fromisoformat(current['timestamp'].replace('Z', '+00:00')) if isinstance(current['timestamp'], str) else current['timestamp']
            prev_time = datetime.fromisoformat(previous['timestamp'].replace('Z', '+00:00')) if isinstance(previous['timestamp'], str) else previous['timestamp']
            days_between = max((curr_time - prev_time).days, 1)

            # Skip comparison if gap is too large (> 30 days) - likely historical data issue
            if days_between > 30:
                print(f"   ⚠️  Warning: Large time gap ({days_between} days) between data points for {link}")
                # Use current data only, set growth to 0
                click_growth = 0
                fan_growth = 0
            else:
                click_growth = current['clicks'] - previous['clicks']
                fan_growth = current['fans'] - previous['fans']

            # Calculate historical averages if we have enough data
            if len(sorted_data) >= 3:
                # Calculate daily growth rates from all historical periods
                daily_growths = []
                for i in range(len(sorted_data) - 1):
                    curr = sorted_data[i]
                    prev = sorted_data[i + 1]

                    # Calculate days between measurements
                    curr_time = datetime.fromisoformat(curr['timestamp'].replace('Z', '+00:00')) if isinstance(curr['timestamp'], str) else curr['timestamp']
                    prev_time = datetime.fromisoformat(prev['timestamp'].replace('Z', '+00:00')) if isinstance(prev['timestamp'], str) else prev['timestamp']
                    days_diff = (curr_time - prev_time).days

                    if days_diff > 0:
                        daily_click_growth = (curr['clicks'] - prev['clicks']) / days_diff
                        daily_fan_growth = (curr['fans'] - prev['fans']) / days_diff
                        daily_growths.append({
                            'clicks': daily_click_growth,
                            'fans': daily_fan_growth
                        })

                # Calculate averages and deviations
                if daily_growths:
                    avg_daily_clicks = mean([g['clicks'] for g in daily_growths])
                    avg_daily_fans = mean([g['fans'] for g in daily_growths])

                    # Current period daily rate (use actual days between points)
                    current_daily_clicks = click_growth / max(days_between, 1)
                    current_daily_fans = fan_growth / max(days_between, 1)

                    # Deviation from average
                    click_deviation = current_daily_clicks - avg_daily_clicks
                    fan_deviation = current_daily_fans - avg_daily_fans
                else:
                    avg_daily_clicks = avg_daily_fans = 0
                    click_deviation = fan_deviation = 0
            else:
                avg_daily_clicks = avg_daily_fans = 0
                click_deviation = fan_deviation = 0

            metrics[link] = {
                'current_clicks': current['clicks'],
                'current_fans': current['fans'],
                'click_growth': click_growth,
                'fan_growth': fan_growth,
                'avg_daily_clicks': avg_daily_clicks,
                'avg_daily_fans': avg_daily_fans,
                'click_deviation': click_deviation,
                'fan_deviation': fan_deviation,
                'conversion_rate': (current['fans'] / current['clicks'] * 100) if current['clicks'] > 0 else 0
            }

    return metrics


def _parse_ts(ts):
    if isinstance(ts, str):
        try:
            return datetime.fromisoformat(ts.replace('Z', '+00:00'))
        except Exception:
            return datetime.fromisoformat(ts)
    return ts


def calculate_trend_metrics(data_by_link, days_window: int = 7):
    """Calculate multi-day trend metrics per link using rolling windows.
    Returns a dict per link with recent vs previous window changes and per-day rates.
    """
    trends = {}
    for link in PRIORITY_LINKS:
        if link not in data_by_link or not data_by_link[link]:
            continue
        entries = sorted(data_by_link[link], key=lambda x: x['timestamp'])  # oldest -> newest
        # Use the link's latest timestamp as reference
        latest_dt = _parse_ts(entries[-1]['timestamp'])
        recent_start = latest_dt - timedelta(days=days_window)
        prev_start = latest_dt - timedelta(days=days_window * 2)

        # Helper to get fans value at or before a cutoff
        def value_at_or_before(cutoff_dt):
            val = None
            time_at_val = None
            for e in entries:
                e_dt = _parse_ts(e['timestamp'])
                if e_dt <= cutoff_dt:
                    val = e['fans']
                    time_at_val = e_dt
                else:
                    break
            # If nothing before cutoff, take earliest known
            if val is None:
                val = entries[0]['fans']
                time_at_val = _parse_ts(entries[0]['timestamp'])
            return val, time_at_val

        fans_latest = entries[-1]['fans']
        fans_recent_start, recent_start_dt_actual = value_at_or_before(recent_start)
        fans_prev_start, prev_start_dt_actual = value_at_or_before(prev_start)

        recent_change = fans_latest - fans_recent_start
        prev_change = fans_recent_start - fans_prev_start

        # Per-day rates based on actual covered days
        recent_days = max((latest_dt - recent_start_dt_actual).days, 1)
        prev_days = max((recent_start_dt_actual - prev_start_dt_actual).days, 1)
        recent_rate = recent_change / recent_days
        prev_rate = prev_change / prev_days

        # Classification
        classification = 'Stable'
        if prev_rate == 0 and recent_rate > 0:
            classification = 'Boost'
        elif prev_rate == 0 and recent_rate < 0:
            classification = 'Dip'
        else:
            delta_rate = recent_rate - prev_rate
            if delta_rate >= 0.5 or recent_change >= prev_change + 3:
                classification = 'Boost'
            elif delta_rate <= -0.5 or recent_change <= prev_change - 3:
                classification = 'Dip'

        trends[link] = {
            'recent_change': recent_change,
            'prev_change': prev_change,
            'recent_rate_per_day': recent_rate,
            'prev_rate_per_day': prev_rate,
            'classification': classification,
            'window_days': days_window
        }
    return trends


def summarize_trends(trends):
    """Return top boosts and dips for display."""
    boosts = [(l, d) for l, d in trends.items() if d['classification'] == 'Boost']
    dips = [(l, d) for l, d in trends.items() if d['classification'] == 'Dip']
    boosts.sort(key=lambda x: (x[1]['recent_rate_per_day'] - x[1]['prev_rate_per_day']), reverse=True)
    dips.sort(key=lambda x: (x[1]['prev_rate_per_day'] - x[1]['recent_rate_per_day']), reverse=True)
    return boosts[:3], dips[:3]

def generate_standardized_report():
    """Generate standardized daily report"""
    # Validate data ordering first
    validate_data_ordering()

    # Get dynamic date range
    start_date, end_date, days_count = get_date_range()

    print("🎯 ONLYMONSTER STANDARDIZED DAILY REPORT")
    print(f"📅 Period: {start_date} → {end_date} ({days_count} days)")
    print("="*60)
    
    # Get data and calculate metrics
    data_by_link = get_historical_data()
    metrics = calculate_growth_metrics(data_by_link)
    
    # 1. NEW SUBSCRIBERS (standardized format)
    print("\n👥 NEW SUBSCRIBERS (Where growth came from):")
    total_new_fans = 0
    total_new_clicks = 0
    
    # Sort by fan growth (descending)
    sorted_by_fans = sorted(metrics.items(), key=lambda x: x[1]['fan_growth'], reverse=True)
    
    for link, data in sorted_by_fans:
        fan_growth = data['fan_growth']
        click_growth = data['click_growth']
        total_new_fans += fan_growth
        total_new_clicks += click_growth
        
        display_name = get_display_name(link)
        if fan_growth > 0:
            print(f"   ✅ {display_name}: +{fan_growth} fans (+{click_growth} clicks)")
        elif fan_growth == 0:
            print(f"   ⚠️  {display_name}: +{fan_growth} fans (+{click_growth} clicks)")
        else:
            print(f"   ❌ {display_name}: {fan_growth} fans (+{click_growth} clicks)")
    
    # 2. TRAFFIC GROWTH (standardized format)
    print(f"\n💰 TRAFFIC GROWTH (Latest activity):")
    sorted_by_clicks = sorted(metrics.items(), key=lambda x: x[1]['click_growth'], reverse=True)
    
    for link, data in sorted_by_clicks[:5]:  # Top 5
        click_growth = data['click_growth']
        display_name = get_display_name(link)
        print(f"   📈 {display_name}: +{click_growth} clicks")
    
    # 3. STAGNANT LINKS
    print(f"\n⚠️  STAGNANT LINKS (No revenue or fans):")
    stagnant_links = [link for link, data in metrics.items() if data['fan_growth'] == 0 and data['click_growth'] == 0]
    
    if stagnant_links:
        for link in stagnant_links:
            print(f"   ❌ {link}: No activity")
    else:
        print("   ✅ All priority links showing activity!")

    # 4. EARNINGS REPORT (Daily Changes)
    print(f"\n💰 EARNINGS REPORT (Past Day):")
    print("   Daily revenue performance by platform:")

    # Get earnings data for current period - focus on daily changes
    earnings_data = []
    total_daily_earnings = 0
    daily_earnings_changes = []

    # Get current and previous earnings from the database
    with sqlite3.connect(DATABASE_PATH) as conn:
        cursor = conn.cursor()

        # Get latest earnings for each link
        cursor.execute('''
            SELECT tracking_link_name, earnings, clicks, fans
            FROM tracking_data t1
            WHERE timestamp = (
                SELECT MAX(timestamp)
                FROM tracking_data t2
                WHERE t2.tracking_link_name = t1.tracking_link_name
            )
            ORDER BY earnings DESC
        ''')
        current_earnings = cursor.fetchall()

        # Get previous earnings for comparison (second most recent)
        cursor.execute('''
            SELECT DISTINCT tracking_link_name, earnings
            FROM tracking_data t1
            WHERE timestamp = (
                SELECT MAX(timestamp)
                FROM tracking_data t2
                WHERE t2.tracking_link_name = t1.tracking_link_name
                AND t2.timestamp < (
                    SELECT MAX(timestamp)
                    FROM tracking_data t3
                    WHERE t3.tracking_link_name = t1.tracking_link_name
                )
            )
        ''')
        previous_earnings = {name: earnings for name, earnings in cursor.fetchall()}

    if current_earnings:
        # Calculate daily changes and collect data
        for name, current, clicks, fans in current_earnings:
            previous = previous_earnings.get(name, 0)
            daily_change = current - previous

            # Only include links with earnings changes (positive or negative)
            if daily_change != 0:
                total_daily_earnings += daily_change
                daily_earnings_changes.append((name, daily_change, current, fans))

        # Sort by daily change (descending)
        daily_earnings_changes.sort(key=lambda x: x[1], reverse=True)

        if daily_earnings_changes:
            for name, daily_change, current_total, fans in daily_earnings_changes:
                # Calculate earnings per fan for context
                epf = current_total / fans if fans > 0 else 0

                if daily_change > 0:
                    print(f"   💰 {name}: +${daily_change:.2f} today | ${epf:.2f}/fan total")
                else:
                    print(f"   📉 {name}: ${daily_change:.2f} today | ${epf:.2f}/fan total")

            print(f"   📊 Total Daily Revenue: ${total_daily_earnings:.2f}")

            # Show top 3 revenue sources by daily increase
            top_daily_earners = [item for item in daily_earnings_changes if item[1] > 0][:3]
            if top_daily_earners:
                print(f"   🏆 Top 3 Daily Revenue Sources:")
                for i, (name, daily_change, current_total, fans) in enumerate(top_daily_earners, 1):
                    print(f"      {i}. {name}: +${daily_change:.2f} today")
        else:
            print("   ➡️  No earnings changes in the past day")
    else:
        print("   ℹ️  No earnings data available for current period")

    # 5. CONVERSION RATE CHANGES
    print(f"\n📊 CONVERSION RATE CHANGES:")
    print("   Rate changes vs previous period:")

    # Calculate conversion rate changes
    conversion_changes = []
    for link, data in metrics.items():
        # Get previous conversion rate from historical data
        if link in data_by_link and len(data_by_link[link]) >= 2:
            sorted_data = sorted(data_by_link[link], key=lambda x: x['timestamp'], reverse=True)
            current = sorted_data[0]
            previous = sorted_data[1]

            current_rate = (current['fans'] / current['clicks'] * 100) if current['clicks'] > 0 else 0
            prev_rate = (previous['fans'] / previous['clicks'] * 100) if previous['clicks'] > 0 else 0
            rate_change = current_rate - prev_rate

            conversion_changes.append((link, current_rate, rate_change))

    # Sort by rate change (most improved first)
    conversion_changes.sort(key=lambda x: x[2], reverse=True)

    for link, current_rate, rate_change in conversion_changes:
        if abs(rate_change) > 0.01:  # Show significant changes
            direction = "📈" if rate_change > 0 else "📉"
            print(f"   {direction} {link}: {current_rate:.2f}% ({rate_change:+.2f}%)")
        else:
            print(f"   ➡️ {link}: {current_rate:.2f}% (stable)")

    if not conversion_changes:
        print("   ➡️ No significant conversion rate changes")
    
    # 6. DEVIATION ANALYSIS (NEW)
    print(f"\n📈 DEVIATION FROM HISTORICAL AVERAGE:")
    print("   Performance vs historical daily averages:")

    for link, data in metrics.items():
        click_dev = data['click_deviation']
        fan_dev = data['fan_deviation']

        if abs(click_dev) > 5 or abs(fan_dev) > 0.5:  # Significant deviation threshold
            click_status = "📈" if click_dev > 0 else "📉" if click_dev < 0 else "➡️"
            fan_status = "📈" if fan_dev > 0 else "📉" if fan_dev < 0 else "➡️"

            print(f"   {click_status} {link}:")
            print(f"      Clicks: {click_dev:+.1f}/day vs avg ({data['avg_daily_clicks']:.1f}/day)")
            print(f"      Fans: {fan_dev:+.2f}/day vs avg ({data['avg_daily_fans']:.2f}/day)")


    # 7. SUMMARY (standardized format)
    print(f"\n📈 SUMMARY:")
    print(f"   Total Growth: +{total_new_fans} fans, +{total_new_clicks:,} clicks")
    print(f"   Daily Average: +{total_new_fans//4} fans, +{total_new_clicks//4:,} clicks")
    print(f"   Active Links: {len([l for l, d in metrics.items() if d['fan_growth'] > 0])}/{len(PRIORITY_LINKS)}")

    return {
        'metrics': metrics,
        'total_new_fans': total_new_fans,
        'total_new_clicks': total_new_clicks,
        'total_earnings': total_daily_earnings if 'total_daily_earnings' in locals() else 0,
        'stagnant_links': stagnant_links,
        'conversion_changes': conversion_changes if 'conversion_changes' in locals() else [],
        'trend_summary': {}
    }

def fix_link_names(text):
    """Fix link names by adding back dashes that AI removes"""
    # Common link name patterns to fix - order matters (longer patterns first)
    replacements = {
        'reelslilfoxnaomiaug22': 'reels-lilfoxnaomi-aug-22',
        'redditbabycheeksxcombined': 'reddit-babycheeksx-combined',
        'redditbabycheeksx': 'reddit-babycheeksx-combined',  # Map to combined
        'reelsnaominoface': 'reels-naominoface',
        'chivenylaaug8': 'chive-nyla-aug-8',
        'tiktokaug124': 'tiktok-aug-1-24',
        'chiveaug124': 'chive-aug-1-24',
        'Reels2024': 'Reels2024',  # This one is correct
        'babycheeksx': 'reddit-babycheeksx-combined',  # Map to combined
        # Additional patterns that might appear
        'reddit babycheeksx': 'reddit-babycheeksx-combined',
        'reddit babycheeksx combined': 'reddit-babycheeksx-combined',
        'tiktok aug 1 24': 'tiktok-aug-1-24',
        'chive nyla aug 8': 'chive-nyla-aug-8',
        'reels lilfoxnaomi aug 22': 'reels-lilfoxnaomi-aug-22',
        'chive aug 1 24': 'chive-aug-1-24',
        'reels naominoface': 'reels-naominoface',
        # Handle variations with different spacing
        'reddit-babycheeksx-combined': 'reddit-babycheeksx-combined',  # Already correct
        'tiktok-aug-1-24': 'tiktok-aug-1-24',  # Already correct
        'chive-nyla-aug-8': 'chive-nyla-aug-8',  # Already correct
        'reels-lilfoxnaomi-aug-22': 'reels-lilfoxnaomi-aug-22',  # Already correct
        'chive-aug-1-24': 'chive-aug-1-24',  # Already correct
        'reels-naominoface': 'reels-naominoface'  # Already correct
    }

    for wrong, correct in replacements.items():
        text = text.replace(wrong, correct)

    return text

def get_display_name(link_name):
    """Get display name for a link (shows individual links for combined entries)"""
    link_combiner = LinkCombiner()
    return link_combiner.get_display_name(link_name)

def format_slack_message(report_data):
    """Format visually clear message for Slack with emojis for quick scanning"""
    metrics = report_data['metrics']

    # Get dynamic date range
    start_date, end_date, days_count = get_date_range()

    message = "🎯 *OnlyMonster Daily Report*\n"
    message += f"📅 {start_date} → {end_date} ({days_count} days)\n"
    message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"

    # NEW SUBSCRIBERS (with visual indicators)
    message += "👥 *NEW SUBSCRIBERS*\n"
    sorted_by_fans = sorted(metrics.items(), key=lambda x: x[1]['fan_growth'], reverse=True)

    for link, data in sorted_by_fans:
        fan_growth = data['fan_growth']
        click_growth = data['click_growth']

        if fan_growth > 0:
            message += f"✅ {link}: +{fan_growth} fans (+{click_growth} clicks)\n"
        elif fan_growth == 0:
            message += f"⚠️ {link}: +{fan_growth} fans (+{click_growth} clicks)\n"
        else:
            message += f"❌ {link}: {fan_growth} fans (+{click_growth} clicks)\n"

    # TRAFFIC GROWTH
    message += f"\n💰 *TRAFFIC GROWTH*\n"
    sorted_by_clicks = sorted(metrics.items(), key=lambda x: x[1]['click_growth'], reverse=True)

    for link, data in sorted_by_clicks[:3]:
        click_growth = data['click_growth']
        message += f"📈 {link}: +{click_growth} clicks\n"

    # CONVERSION RATE CHANGES
    message += f"\n📊 *CONVERSION RATE CHANGES*\n"
    conversion_changes = report_data.get('conversion_changes', [])

    if conversion_changes:
        # Show top 3 most significant changes
        significant_changes = [c for c in conversion_changes if abs(c[2]) > 0.01][:3]
        if significant_changes:
            for link, current_rate, rate_change in significant_changes:
                direction = "📈" if rate_change > 0 else "📉"
                message += f"{direction} {link}: {current_rate:.2f}% ({rate_change:+.2f}%)\n"
        else:
            message += "➡️ No significant conversion changes\n"
    else:
        message += "➡️ Conversion rates stable\n"

    # PERFORMANCE vs AVERAGE
    message += f"\n📊 *PERFORMANCE vs AVERAGE*\n"
    significant_deviations = []

    for link, data in metrics.items():
        click_dev = data['click_deviation']
        fan_dev = data['fan_deviation']

        if abs(click_dev) > 5 or abs(fan_dev) > 0.5:
            status = "📈" if (click_dev > 0 or fan_dev > 0) else "📉"
            significant_deviations.append(f"{status} {link}: {click_dev:+.0f} clicks/day, {fan_dev:+.1f} fans/day")

    if significant_deviations:
        for dev in significant_deviations[:3]:  # Top 3
            message += f"{dev}\n"
    else:
        message += "➡️ All links performing within normal range\n"


    # EARNINGS SUMMARY (Daily Changes)
    total_daily_earnings = report_data.get('total_earnings', 0)
    if total_daily_earnings != 0:
        message += f"\n💰 *EARNINGS SUMMARY (Past Day)*\n"
        message += f"• Daily Revenue: ${total_daily_earnings:.2f}\n"

        # Get top daily earning changes from database
        try:
            with sqlite3.connect(DATABASE_PATH) as conn:
                cursor = conn.cursor()

                # Get current earnings
                cursor.execute('''
                    SELECT tracking_link_name, earnings, fans
                    FROM tracking_data t1
                    WHERE timestamp = (
                        SELECT MAX(timestamp)
                        FROM tracking_data t2
                        WHERE t2.tracking_link_name = t1.tracking_link_name
                    )
                    ORDER BY earnings DESC
                ''')
                current_earnings = cursor.fetchall()

                # Get previous earnings for comparison
                cursor.execute('''
                    SELECT DISTINCT tracking_link_name, earnings
                    FROM tracking_data t1
                    WHERE timestamp = (
                        SELECT MAX(timestamp)
                        FROM tracking_data t2
                        WHERE t2.tracking_link_name = t1.tracking_link_name
                        AND t2.timestamp < (
                            SELECT MAX(timestamp)
                            FROM tracking_data t3
                            WHERE t3.tracking_link_name = t1.tracking_link_name
                        )
                    )
                ''')
                previous_earnings = {name: earnings for name, earnings in cursor.fetchall()}

                # Calculate daily changes and get top 3
                daily_changes = []
                for name, current, fans in current_earnings:
                    previous = previous_earnings.get(name, 0)
                    daily_change = current - previous
                    if daily_change > 0:
                        daily_changes.append((name, daily_change, current, fans))

                # Sort by daily change and get top 3
                daily_changes.sort(key=lambda x: x[1], reverse=True)
                top_daily_earners = daily_changes[:3]

                if top_daily_earners:
                    message += f"• Top Daily Revenue Sources:\n"
                    for i, (name, daily_change, current_total, fans) in enumerate(top_daily_earners, 1):
                        message += f"  {i}. {name}: +${daily_change:.2f} today\n"
        except Exception as e:
            message += f"• Daily earnings: ${total_daily_earnings:.2f}\n"

    # SUMMARY
    total_fans = report_data['total_new_fans']
    total_clicks = report_data['total_new_clicks']

    message += f"\n📈 *SUMMARY*\n"
    message += f"• +{total_fans} fans, +{total_clicks:,} clicks\n"
    message += f"• {total_fans//4} fans/day, {total_clicks//4:,} clicks/day\n"

    message += f"\n🕐 {datetime.now().strftime('%H:%M %m/%d/%y')}"

    return message

def main():
    """Main function"""
    # Generate standardized report
    report_data = generate_standardized_report()
    
    # Send to Slack
    print(f"\n📤 SENDING TO SLACK...")
    print("-" * 25)
    
    try:
        slack_notifier = SlackWebhookNotifier()
        message = format_slack_message(report_data)
        
        success = slack_notifier.send_message_via_api(message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)
        
        if success:
            print("✅ Standardized report sent to Slack successfully!")
            print(f"📱 Check: https://cheeksglobal.slack.com/archives/{SLACK_CHANNEL_ID}")
        else:
            print("❌ Failed to send to Slack")
            
    except Exception as e:
        print(f"❌ Slack error: {e}")

if __name__ == "__main__":
    main()
