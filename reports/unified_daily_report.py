#!/usr/bin/env python3
"""
Unified Daily Report - Combines Social Media Activity and OnlyMonster Analytics
Provides comprehensive daily updates including social media posts and revenue insights
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
import pytz
from datetime import datetime, timedelta, date
from typing import Dict, Any

# Import OnlyMonster functionality
from reports.standardized_daily_report import generate_standardized_report, format_slack_message
from core.onlymonster_scraper import OnlyMonsterScraper
from core.analytics import TrackingAnalytics
from core.config import SLACK_BOT_TOKEN, SLACK_CHANNEL_ID

# Import Social Media functionality
from social.multi_platform_checker import <PERSON><PERSON><PERSON><PERSON><PERSON>he<PERSON>, get_24_hour_window
from social.onlyfans_checker import OnlyFans<PERSON>hecker
from slack.slack_webhook import SlackWebhookNotifier

def setup_logging():
    """Set up logging configuration."""
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger

def get_social_media_report(time_window=None):
    """Generate social media activity report."""
    logger = logging.getLogger(__name__)
    
    try:
        if time_window:
            start_time, end_time = time_window
            checker = MultiPlatformChecker(time_window=time_window)
            logger.info(f"Checking social media activity from {start_time} to {end_time}")
        else:
            # Use 24-hour window by default
            start_time, end_time = get_24_hour_window()
            checker = MultiPlatformChecker(time_window=(start_time, end_time))
            logger.info(f"Checking social media activity in 24-hour window: {start_time} to {end_time}")
        
        # Check all platforms
        results = checker.check_all_platforms()
        
        # Generate status message
        status_message = checker.get_status_message(results)
        
        # Check OnlyFans deals
        logger.info("Checking OnlyFans deals...")
        onlyfans_checker = OnlyFansChecker()
        deal_info = onlyfans_checker.check_deal_status("foxnaomi")
        deal_message = onlyfans_checker.get_deal_status_message(deal_info, "foxnaomi")
        
        # Combine social media status with OnlyFans deal status
        combined_message = status_message + "\n\n💎 **ONLYFANS DEALS**\n" + deal_message
        
        return {
            'success': True,
            'message': combined_message,
            'results': results,
            'summary': results['summary'],
            'onlyfans_deal': deal_info
        }
        
    except Exception as e:
        logger.error(f"Error generating social media report: {e}")
        return {
            'success': False,
            'error': str(e),
            'message': f"❌ Social Media Check Failed: {str(e)}"
        }

def get_onlymonster_report(run_scraper=True):
    """Generate OnlyMonster analytics report."""
    logger = logging.getLogger(__name__)
    
    try:
        # Optionally run fresh scraping
        if run_scraper:
            logger.info("Running fresh OnlyMonster scrape...")
            scraper = OnlyMonsterScraper()
            scrape_data = scraper.run_scraping()
            
            if scrape_data:
                logger.info(f"Successfully scraped {len(scrape_data)} tracking links")
            else:
                logger.warning("No data collected from scraper")
        
        # Generate standardized report
        logger.info("Generating OnlyMonster analytics report...")
        report_data = generate_standardized_report()
        
        return {
            'success': True,
            'report_data': report_data
        }
        
    except Exception as e:
        logger.error(f"Error generating OnlyMonster report: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def format_unified_message(social_media_report, onlymonster_report):
    """Format unified daily report message combining both social media and OnlyMonster data."""
    
    # Start with header
    lines = ["🌅 **DAILY REPORT**"]
    lines.append(f"📅 {datetime.now().strftime('%B %d, %Y')}")
    lines.append("=" * 50)
    
    # Social Media Activity Section
    lines.append("\n📱 **SOCIAL MEDIA ACTIVITY**")
    lines.append("-" * 30)
    
    if social_media_report['success']:
        lines.append(social_media_report['message'])
    else:
        lines.append(f"❌ Social Media Check Failed: {social_media_report.get('error', 'Unknown error')}")
    
    # OnlyMonster Analytics Section
    lines.append("\n💰 **ONLYMONSTER ANALYTICS**")
    lines.append("-" * 30)
    
    if onlymonster_report['success']:
        # Use the existing format_slack_message function
        onlymonster_message = format_slack_message(onlymonster_report['report_data'])
        # Remove the header from OnlyMonster message since we have our own
        onlymonster_lines = onlymonster_message.split('\n')
        # Skip first few lines that contain the header
        start_idx = 0
        for i, line in enumerate(onlymonster_lines):
            if "👥 NEW SUBSCRIBERS" in line:
                start_idx = i
                break
        lines.extend(onlymonster_lines[start_idx:])
    else:
        lines.append(f"❌ OnlyMonster Report Failed: {onlymonster_report.get('error', 'Unknown error')}")
    
    return '\n'.join(lines)

def send_unified_report(message):
    """Send the unified report to Slack."""
    logger = logging.getLogger(__name__)
    
    try:
        slack_notifier = SlackWebhookNotifier()
        success = slack_notifier.send_message_via_api(message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)
        
        if success:
            logger.info("✅ Unified daily report sent to Slack successfully!")
            logger.info(f"📱 Check: https://cheeksglobal.slack.com/archives/{SLACK_CHANNEL_ID}")
            return True
        else:
            logger.error("❌ Failed to send unified report to Slack")
            return False
            
    except Exception as e:
        logger.error(f"❌ Slack error: {e}")
        return False

def main(scrape_fresh=True, time_window=None):
    """Main function to generate and send unified daily report."""
    logger = setup_logging()
    
    logger.info("🚀 Starting Unified Daily Report Generation")
    logger.info("=" * 60)
    
    # Generate social media report
    logger.info("\n📱 Generating Social Media Report...")
    social_media_report = get_social_media_report(time_window)
    
    # Generate OnlyMonster report  
    logger.info("\n💰 Generating OnlyMonster Report...")
    onlymonster_report = get_onlymonster_report(run_scraper=scrape_fresh)
    
    # Format unified message
    logger.info("\n📝 Formatting Unified Report...")
    unified_message = format_unified_message(social_media_report, onlymonster_report)
    
    # Send to Slack
    logger.info("\n📤 Sending to Slack...")
    success = send_unified_report(unified_message)
    
    # Log summary
    logger.info("\n📊 REPORT SUMMARY:")
    logger.info(f"  Social Media: {'✅' if social_media_report['success'] else '❌'}")
    logger.info(f"  OnlyMonster: {'✅' if onlymonster_report['success'] else '❌'}")
    logger.info(f"  Slack Delivery: {'✅' if success else '❌'}")
    
    return {
        'success': social_media_report['success'] and onlymonster_report['success'] and success,
        'social_media_report': social_media_report,
        'onlymonster_report': onlymonster_report,
        'slack_success': success
    }

if __name__ == "__main__":
    # Parse command line arguments
    scrape_fresh = True
    time_window = None
    
    if "--no-scrape" in sys.argv:
        scrape_fresh = False
        
    if "--24hour" in sys.argv:
        time_window = get_24_hour_window()
    
    # Run the unified report
    result = main(scrape_fresh=scrape_fresh, time_window=time_window)
    
    if result['success']:
        print("✅ Unified daily report completed successfully!")
        sys.exit(0)
    else:
        print("❌ Unified daily report encountered errors")
        sys.exit(1)