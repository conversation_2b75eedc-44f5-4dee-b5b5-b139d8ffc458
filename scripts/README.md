# Scripts

- run_onlymonster_scraper.py: runs the daily OnlyMonster scraper (systemd service).
- run_onlymonster_login_assist.py: opens Chrome with a persistent Selenium profile so you can manually log into OnlyMonster when cookies expire. After that, normal scraper runs will restore the session from the profile.

Manual login assist usage:

```
HEADLESS=false PYTHONPATH=/root/onlymonster-automations python3 scripts/run_onlymonster_login_assist.py
```

This opens a local Chrome window. Log in via the UI (use Next then Password). Close the window. Future scheduled runs will use the saved profile and cookies.

