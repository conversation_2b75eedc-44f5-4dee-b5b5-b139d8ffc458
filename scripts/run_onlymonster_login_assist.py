#!/usr/bin/env python3
"""
Manual login helper for OnlyMonster: opens a non-headless browser using the same
persistent Selenium profile. Use this to refresh session when cookies expire.

Steps:
1) It will launch Chrome with the persisted profile.
2) Navigate to https://onlymonster.ai/auth/signin.
3) You complete the login manually (email -> next -> password) in the UI.
4) Close the browser. The session persists in the profile dir for future runs.

Run:
  HEADLESS=false PYTHONPATH=/root/onlymonster-automations python3 scripts/run_onlymonster_login_assist.py
"""
import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from core.config import HEADLESS, LOGIN_URL, SELENIUM_PROFILE_DIR

def main():
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1366,768")
    # force non-headless for manual login
    if HEADLESS:
        print("For manual login, set HEADLESS=false in env.")
    # persistent profile
    os.makedirs(SELENIUM_PROFILE_DIR, exist_ok=True)
    chrome_options.add_argument(f"--user-data-dir={SELENIUM_PROFILE_DIR}")
    chrome_options.add_argument("--profile-directory=Default")

    driver = webdriver.Chrome(options=chrome_options)
    driver.get(LOGIN_URL)
    print("Browser opened. Please complete the login and then close the window.")
    # Keep process up to 10 minutes to allow manual login
    try:
        for _ in range(600):
            time.sleep(1)
            if not driver.service.is_connectable():
                break
    finally:
        try:
            driver.quit()
        except Exception:
            pass

if __name__ == "__main__":
    main()

