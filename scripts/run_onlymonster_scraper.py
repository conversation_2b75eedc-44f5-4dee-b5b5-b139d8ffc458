#!/usr/bin/env python3
"""
Small runner to execute the OnlyMonster scraper (for systemd ExecStart).
Exits non-zero on failure.
"""
import sys

from core.onlymonster_scraper import OnlyMonsterScraper


def main() -> int:
    print("🎯 Starting OnlyMonster data collection (systemd runner)...")
    try:
        scraper = OnlyMonsterScraper()
        tracking_data = scraper.run_scraping()
        if tracking_data:
            print(f"✅ Successfully collected {len(tracking_data)} OnlyMonster tracking links")
            return 0
        else:
            print("⚠️ No tracking data collected")
            return 1
    except Exception as e:
        print(f"❌ OnlyMonster scraping failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

