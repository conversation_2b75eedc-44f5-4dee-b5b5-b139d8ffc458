#!/usr/bin/env python3
"""
Launch a visible Chrome (no headless) in a virtual display so a human can complete
OnlyMonster login, then save session cookies to ONLYMONSTER_COOKIE_FILE.

Usage (we run this with DISPLAY set by the caller):
  HEADLESS=false python3 scripts/seed_onlymonster_cookies.py

This script will:
- Start Selenium Chrome (non-headless)
- Navigate to LOGIN_URL
- Wait up to 10 minutes for redirect away from /auth
- Save cookies to ONLYMONSTER_COOKIE_FILE
"""
import os
import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Reuse config constants
from core.config import LOGIN_URL, ONLYMONSTER_COOKIE_FILE


def build_options() -> Options:
    o = Options()
    # Force headful (visible) for seeding
    # (We rely on the caller to set DISPLAY=:99 via xvfb)
    o.add_argument("--no-sandbox")
    o.add_argument("--disable-dev-shm-usage")
    o.add_argument("--window-size=1920,1080")
    o.add_argument("--disable-gpu")
    o.add_argument("--disable-extensions")
    o.add_argument("--disable-plugins")
    o.add_argument("--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36")

    # Use the same binary selection logic we added in scraper
    for candidate in [
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser",
        "/snap/bin/chromium",
    ]:
        if os.path.exists(candidate):
            o.binary_location = candidate
            print(f"Using Chrome binary at: {candidate}")
            break
    return o


def main():
    print("=== OnlyMonster Cookie Seeder ===")
    print("This will open Chrome in the VNC session.")
    print("Please complete the login manually. Once you land on the dashboard/panel, cookies will be saved automatically.")

    options = build_options()

    # Prefer Selenium Manager (auto driver)
    driver = webdriver.Chrome(options=options)
    driver.set_page_load_timeout(60)

    try:
        print(f"Opening login URL: {LOGIN_URL}")
        driver.get(LOGIN_URL)

        start = time.time()
        timeout_sec = 600  # 10 minutes

        # Poll until we are away from auth (user finishes login)
        while True:
            try:
                current = driver.current_url
            except Exception:
                current = ""
            if current and "auth" not in current.lower():
                print(f"Detected redirect away from auth: {current}")
                break
            elapsed = int(time.time() - start)
            if elapsed % 15 == 0:
                print(f"Still waiting for manual login... {elapsed}s elapsed (timeout {timeout_sec}s)")
            if elapsed > timeout_sec:
                raise TimeoutError("Timed out waiting for manual login.")
            time.sleep(2)

        # Save cookies
        cookies = driver.get_cookies()
        os.makedirs(os.path.dirname(ONLYMONSTER_COOKIE_FILE), exist_ok=True)
        with open(ONLYMONSTER_COOKIE_FILE, "w") as f:
            json.dump(cookies, f)
        print(f"Saved session cookies to: {ONLYMONSTER_COOKIE_FILE}")

    finally:
        try:
            driver.quit()
        except Exception:
            pass


if __name__ == "__main__":
    main()

