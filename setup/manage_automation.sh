#!/bin/bash

# OnlyMonster Automation Management Script
# Easy commands to manage your daily automation

echo "🤖 OnlyMonster Automation Manager"
echo "=================================="

case "$1" in
    "status")
        echo "📊 Checking automation status..."
        echo ""
        echo "Timer Status:"
        sudo systemctl status onlymonster-daily.timer --no-pager
        echo ""
        echo "Next Scheduled Runs:"
        sudo systemctl list-timers onlymonster-daily.timer --no-pager
        ;;
    
    "logs")
        echo "📋 Viewing recent logs..."
        sudo journalctl -u onlymonster-daily -n 20 --no-pager
        ;;
    
    "logs-live")
        echo "📋 Viewing live logs (Ctrl+C to exit)..."
        sudo journalctl -u onlymonster-daily -f
        ;;
    
    "test")
        echo "🧪 Running test report now..."
        sudo systemctl start onlymonster-daily.service
        echo "✅ Test started! Check logs with: ./manage_automation.sh logs"
        ;;
    
    "stop")
        echo "🛑 Stopping automation..."
        sudo systemctl stop onlymonster-daily.timer
        echo "✅ Automation stopped"
        ;;
    
    "start")
        echo "▶️ Starting automation..."
        sudo systemctl start onlymonster-daily.timer
        echo "✅ Automation started"
        ;;
    
    "restart")
        echo "🔄 Restarting automation..."
        sudo systemctl restart onlymonster-daily.timer
        echo "✅ Automation restarted"
        ;;
    
    "disable")
        echo "❌ Disabling automation..."
        sudo systemctl disable onlymonster-daily.timer
        sudo systemctl stop onlymonster-daily.timer
        echo "✅ Automation disabled"
        ;;
    
    "enable")
        echo "✅ Enabling automation..."
        sudo systemctl enable onlymonster-daily.timer
        sudo systemctl start onlymonster-daily.timer
        echo "✅ Automation enabled and started"
        ;;
    
    *)
        echo "Usage: $0 {command}"
        echo ""
        echo "Available commands:"
        echo "  status      - Check if automation is running"
        echo "  logs        - View recent logs"
        echo "  logs-live   - View live logs"
        echo "  test        - Run a test report now"
        echo "  stop        - Stop the automation"
        echo "  start       - Start the automation"
        echo "  restart     - Restart the automation"
        echo "  disable     - Disable automation completely"
        echo "  enable      - Enable and start automation"
        echo ""
        echo "Examples:"
        echo "  $0 status"
        echo "  $0 test"
        echo "  $0 logs"
        ;;
esac
