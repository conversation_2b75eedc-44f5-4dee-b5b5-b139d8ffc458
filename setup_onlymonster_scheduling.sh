#!/bin/bash

# OnlyMonster Data Collection Setup
# Runs OnlyMonster scraping separately from social media monitoring

echo "Setting up OnlyMonster data collection automation..."

# Create the systemd service for OnlyMonster scraping
sudo tee /etc/systemd/system/onlymonster-scraper.service > /dev/null <<EOF
[Unit]
Description=OnlyMonster Tracking Data Collection
After=network.target

[Service]
Type=oneshot
User=root
WorkingDirectory=/root/onlymonster-automations
Environment=PYTHONPATH=/root/onlymonster-automations
Environment=HEADLESS=true
ExecStart=/usr/bin/python3 /root/onlymonster-automations/scripts/run_onlymonster_scraper.py
StandardOutput=journal
StandardError=journal
TimeoutStartSec=900
EOF

# Create timer for OnlyMonster (once daily at 06:00)
sudo tee /etc/systemd/system/onlymonster-scraper.timer > /dev/null <<EOF
[Unit]
Description=Run OnlyMonster scraper daily at 06:00
Requires=onlymonster-scraper.service

[Timer]
OnCalendar=*-*-* 06:00:00
Persistent=true
RandomizedDelaySec=300

[Install]
WantedBy=timers.target
EOF

# Create a combined service that runs both (for manual execution)
sudo tee /etc/systemd/system/complete-data-collection.service > /dev/null <<EOF
[Unit]
Description=Complete Data Collection (Social + OnlyMonster)
After=network.target

[Service]
Type=oneshot
User=root
WorkingDirectory=/root/onlymonster-automations
Environment=PYTHONPATH=/root/onlymonster-automations
ExecStart=/usr/bin/python3 /root/onlymonster-automations/store_integrated_data.py
StandardOutput=journal
StandardError=journal
TimeoutStartSec=900
EOF

# Reload systemd
sudo systemctl daemon-reload

echo "✅ OnlyMonster scheduling setup complete!"
echo ""
echo "📅 SCHEDULING OPTIONS:"
echo ""
echo "🎯 OnlyMonster Only (2x daily - 7AM, 7PM):"
echo "  sudo systemctl enable onlymonster-scraper.timer"
echo "  sudo systemctl start onlymonster-scraper.timer"
echo ""
echo "📱 Social Media Only (4x daily):"
echo "  sudo systemctl enable social-media-smart.timer"  
echo "  sudo systemctl start social-media-smart.timer"
echo ""
echo "🔄 MANUAL COMMANDS:"
echo ""
echo "⚡ Run OnlyMonster scraping now:"
echo "  sudo systemctl start onlymonster-scraper.service"
echo ""
echo "📱 Run social media check now:"
echo "  python store_social_data_optimized.py"
echo ""
echo "🎯 Run everything together (may take 5+ minutes):"
echo "  sudo systemctl start complete-data-collection.service"
echo ""
echo "📊 Check status:"
echo "  sudo systemctl status onlymonster-scraper.timer"
echo "  sudo systemctl status social-media-smart.timer"
echo ""
echo "🔍 View logs:"
echo "  sudo journalctl -u onlymonster-scraper.service -f"
echo "  sudo journalctl -u social-media-smart.service -f"
echo ""
echo "💡 RECOMMENDED SETUP:"
echo "  1. Enable social media monitoring (fast, 4x daily)"
echo "  2. Enable OnlyMonster scraping (slower, 2x daily)"
echo "  3. This gives you comprehensive monitoring without conflicts!"