#!/bin/bash

# Smart Social Media Monitoring Setup
# Updates 4 times daily during active hours: 9 AM, 1 PM, 6 PM, 10 PM

echo "Setting up smart social media monitoring..."

# Create the systemd service
sudo tee /etc/systemd/system/social-media-smart.service > /dev/null <<EOF
[Unit]
Description=Smart Social Media Data Collection
After=network.target

[Service]
Type=oneshot
User=root
WorkingDirectory=/root/onlymonster-automations
Environment=PYTHONPATH=/root/onlymonster-automations
ExecStart=/usr/bin/python3 /root/onlymonster-automations/store_social_data_optimized.py
StandardOutput=journal
StandardError=journal
EOF

# Create the systemd timer for multiple daily runs
sudo tee /etc/systemd/system/social-media-smart.timer > /dev/null <<EOF
[Unit]
Description=Run social media monitoring 4x daily
Requires=social-media-smart.service

[Timer]
# Run at 9 AM, 1 PM, 6 PM, and 10 PM daily
OnCalendar=*-*-* 09:00:00
OnCalendar=*-*-* 13:00:00
OnCalendar=*-*-* 18:00:00
OnCalendar=*-*-* 22:00:00
Persistent=true

[Install]
WantedBy=timers.target
EOF

# Create a conservative daily-only option
sudo tee /etc/systemd/system/social-media-daily.service > /dev/null <<EOF
[Unit]
Description=Daily Social Media Data Collection
After=network.target

[Service]
Type=oneshot
User=root
WorkingDirectory=/root/onlymonster-automations
Environment=PYTHONPATH=/root/onlymonster-automations
ExecStart=/usr/bin/python3 /root/onlymonster-automations/store_social_data_optimized.py
StandardOutput=journal
StandardError=journal
EOF

sudo tee /etc/systemd/system/social-media-daily.timer > /dev/null <<EOF
[Unit]
Description=Run social media monitoring once daily
Requires=social-media-daily.service

[Timer]
OnCalendar=daily
OnCalendar=*-*-* 06:00:00
Persistent=true

[Install]
WantedBy=timers.target
EOF

# Reload systemd
sudo systemctl daemon-reload

echo "✅ Smart monitoring setup complete!"
echo ""
echo "Choose your monitoring frequency:"
echo ""
echo "🚀 ACTIVE MONITORING (4x daily - 9AM, 1PM, 6PM, 10PM):"
echo "  sudo systemctl enable social-media-smart.timer"
echo "  sudo systemctl start social-media-smart.timer"
echo ""
echo "🛡️  CONSERVATIVE MONITORING (1x daily - 6AM):"
echo "  sudo systemctl enable social-media-daily.timer"
echo "  sudo systemctl start social-media-daily.timer"
echo ""
echo "📊 Check status:"
echo "  sudo systemctl status social-media-smart.timer"
echo "  sudo systemctl status social-media-daily.timer"
echo ""
echo "🔍 View logs:"
echo "  sudo journalctl -u social-media-smart.service -f"
echo "  sudo journalctl -u social-media-daily.service -f"
echo ""
echo "⚡ Run manually:"
echo "  sudo systemctl start social-media-smart.service"
echo "  sudo systemctl start social-media-daily.service"