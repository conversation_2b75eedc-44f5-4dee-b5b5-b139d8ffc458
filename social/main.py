"""Main application logic for TikTok Post Notifier."""

import logging
import sys
from datetime import datetime

from core import config
from social.tiktok_monitor import TikTokMonitor
from social.notifier import Notifier

def setup_logging():
    """Set up logging configuration."""
    pass # config module already imported
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Set up root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, config.LOG_LEVEL))
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    if config.LOG_FILE:
        file_handler = logging.FileHandler(config.LOG_FILE)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def run_check():
    """Run a single check for TikTok posts."""
    logger = logging.getLogger(__name__)
    
    try:
        # Validate configuration
        pass # Config validation removed
        
        logger.info("Starting TikTok post check...")
        accounts = [acc['username'] for acc in config.TIKTOK_ACCOUNTS]
        logger.info(f"Checking {len(accounts)} accounts for posts today: {', '.join(['@' + acc for acc in accounts])}")
        
        # Initialize components - use multi-account monitor
        from social.multi_account_tiktok_monitor import MultiAccountTikTokMonitor
        monitor = MultiAccountTikTokMonitor()
        notifier = Notifier()

        # Check for posts across all accounts
        results = monitor.check_all_accounts_today()

        # Determine if any account has posts today
        has_posts_today = len(results['accounts_with_posts']) > 0

        # Generate status message
        status_message = monitor.get_status_message(results)
        
        # Send notifications
        notification_results = notifier.send_notification(status_message)
        
        # Log results
        logger.info(f"Check completed. Status: {status_message}")
        logger.info(f"Notification results: {notification_results}")
        
        return {
            'success': True,
            'has_posts_today': has_posts_today,
            'message': status_message,
            'notification_results': notification_results,
            'results': results
        }
        
    except Exception as e:
        error_msg = f"Error during check: {e}"
        logger.error(error_msg)
        
        # Try to send error notification
        try:
            notifier = Notifier()
            notifier.send_notification(f"❌ TikTok Monitor Error: {error_msg}")
        except:
            pass  # Don't fail if error notification fails
        
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """Main entry point for single check."""
    # Set up logging
    logger = setup_logging()
    
    logger.info("TikTok Post Notifier - Single Check Mode")
    logger.info(f"Timestamp: {datetime.now()}")
    
    # Run check
    result = run_check()
    
    if result['success']:
        logger.info("Check completed successfully")
        sys.exit(0)
    else:
        logger.error(f"Check failed: {result.get('error', 'Unknown error')}")
        sys.exit(1)

if __name__ == "__main__":
    main()
