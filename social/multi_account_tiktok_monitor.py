"""Multi-account TikTok monitoring using RapidAPI."""

import logging
import json
import http.client
from datetime import datetime, timedelta, date
from typing import List, Optional, Dict, Any, Tuple

from core import config

logger = logging.getLogger(__name__)

class MultiAccountTikTokMonitor:
    """TikTok monitor for multiple accounts using RapidAPI."""
    
    def __init__(self):
        self.config = config
    
    def _make_api_request(self, sec_uid: str, username: str) -> Optional[Dict[str, Any]]:
        """Make request to TikTok API via RapidAPI for a specific account."""
        try:
            conn = http.client.HTTPSConnection("tiktok-api23.p.rapidapi.com")
            
            headers = {
                'x-rapidapi-key': self.config.RAPIDAPI_KEY,
                'x-rapidapi-host': "tiktok-api23.p.rapidapi.com"
            }
            
            # Request recent posts for the user
            endpoint = f"/api/user/posts?secUid={sec_uid}&count=35&cursor=0"
            
            logger.info(f"Making API request to get posts for @{username}")
            conn.request("GET", endpoint, headers=headers)
            
            res = conn.getresponse()
            data = res.read()
            
            if res.status == 200:
                response_data = json.loads(data.decode("utf-8"))
                logger.info(f"Successfully retrieved data from TikTok API for @{username}")
                return response_data
            else:
                logger.error(f"API request failed for @{username} with status {res.status}: {data.decode('utf-8')}")
                return None
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse API response as JSON for @{username}: {e}")
            return None
        except Exception as e:
            logger.error(f"API request failed for @{username}: {e}")
            return None
        finally:
            try:
                conn.close()
            except:
                pass
    
    def _parse_post_dates(self, api_response: Dict[str, Any], username: str) -> List[datetime]:
        """Parse post dates from API response."""
        post_dates = []
        
        try:
            # Get posts from the response
            posts = []
            if 'data' in api_response:
                data = api_response['data']

                # Handle case where data is a JSON string (new API format)
                if isinstance(data, str):
                    try:
                        data = json.loads(data)
                        logger.debug(f"@{username} Parsed JSON string in data field")
                    except json.JSONDecodeError as e:
                        logger.error(f"@{username} Failed to parse JSON string in data field: {e}")
                        data = {}

                # Now extract posts from the parsed data
                if isinstance(data, dict) and 'itemList' in data:
                    posts = data['itemList']
                else:
                    # Debug: Log the actual response structure
                    logger.debug(f"@{username} API response keys: {list(api_response.keys()) if isinstance(api_response, dict) else 'Not a dict'}")
                    logger.debug(f"@{username} data type: {type(data)}, keys: {list(data.keys()) if isinstance(data, dict) else 'not dict'}")

            logger.info(f"Found {len(posts)} posts for @{username}")
            
            for i, post in enumerate(posts):
                try:
                    # Debug: Check what type post is
                    if not isinstance(post, dict):
                        logger.debug(f"@{username} Post {i+1}: Expected dict but got {type(post)}: {post}")
                        continue

                    # Get createTime timestamp
                    timestamp = post.get('createTime')
                    
                    if timestamp:
                        # Handle both seconds and milliseconds timestamps
                        if isinstance(timestamp, str):
                            timestamp = int(timestamp)
                        
                        if timestamp > 1000000000000:  # Milliseconds
                            timestamp = timestamp / 1000
                        
                        post_date = datetime.fromtimestamp(timestamp)
                        post_dates.append(post_date)
                        logger.debug(f"@{username} Post {i+1}: {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                    else:
                        logger.debug(f"@{username} Post {i+1}: No timestamp found")
                    
                except (ValueError, KeyError, TypeError) as e:
                    logger.debug(f"@{username} Post {i+1}: Could not parse timestamp: {e}")
                    continue
            
            logger.info(f"Successfully parsed {len(post_dates)} post dates for @{username}")
            return post_dates
            
        except Exception as e:
            logger.error(f"Error parsing post dates for @{username}: {e}")
            # Debug: Log the actual response structure when there's an error
            logger.debug(f"@{username} Full API response: {str(api_response)[:500]}...")
            return []
    
    def check_account_posts_today(self, account: Dict[str, str]) -> Tuple[bool, List[datetime]]:
        """Check if there were any posts made today for a specific account."""
        username = account['username']
        sec_uid = account['secUid']
        
        try:
            logger.info(f"Checking posts for @{username}...")
            
            # Make API request
            api_response = self._make_api_request(sec_uid, username)
            if not api_response:
                logger.error(f"❌ API ERROR: Failed to get data from TikTok API for @{username} - API request failed")
                return False, []

            # Parse post dates
            post_dates = self._parse_post_dates(api_response, username)

            if not post_dates:
                logger.info(f"✅ @{username}: API successful but no posts found in account")
                return False, []
            
            # Check if any posts were made on the target date
            target_date = datetime.now().date()  # Default to today
            target_posts = []

            for post_date in post_dates:
                if post_date.date() == target_date:
                    target_posts.append(post_date)
            
            if today_posts:
                logger.info(f"@{username}: Found {len(today_posts)} post(s) from today:")
                for post_date in today_posts:
                    logger.info(f"  - Post at: {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                return True, today_posts
            else:
                logger.info(f"✅ @{username}: API successful but no posts found from today")
                # Show the most recent posts for debugging
                if post_dates:
                    recent_posts = sorted(post_dates, reverse=True)[:2]
                    logger.info(f"@{username}: Most recent posts:")
                    for post_date in recent_posts:
                        logger.info(f"  - {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                return False, []
            
        except Exception as e:
            logger.error(f"❌ SYSTEM ERROR: Error checking posts for @{username}: {e}")
            return False, []
    
    def check_all_accounts_today(self) -> Dict[str, Any]:
        """Check all configured accounts for posts today."""
        results = {
            'accounts_checked': [],
            'accounts_with_posts': [],
            'accounts_without_posts': [],
            'total_posts_today': 0,
            'summary': {}
        }
        
        logger.info("Starting multi-account TikTok check...")
        logger.info(f"Checking {len(self.config.TIKTOK_ACCOUNTS)} accounts")
        
        for account in self.config.TIKTOK_ACCOUNTS:
            username = account['username']
            has_posts, today_posts = self.check_account_posts_today(account)
            
            account_result = {
                'username': username,
                'has_posts_today': has_posts,
                'posts_count': len(today_posts),
                'post_times': [post.strftime('%H:%M:%S') for post in today_posts]
            }
            
            results['accounts_checked'].append(account_result)
            results['summary'][username] = account_result
            
            if has_posts:
                results['accounts_with_posts'].append(username)
                results['total_posts_today'] += len(today_posts)
            else:
                results['accounts_without_posts'].append(username)
        
        # Log summary
        logger.info("Multi-account check completed:")
        logger.info(f"  Accounts with posts today: {len(results['accounts_with_posts'])}")
        logger.info(f"  Accounts without posts today: {len(results['accounts_without_posts'])}")
        logger.info(f"  Total posts found today: {results['total_posts_today']}")
        
        return results

    def check_account_posts_for_date(self, account: Dict[str, str], target_date: date) -> Tuple[bool, List[datetime]]:
        """Check if there were any posts made on a specific date for a specific account."""
        try:
            username = account['username']
            sec_uid = account['secUid']

            logger.info(f"Checking posts for @{username} on {target_date.strftime('%Y-%m-%d')}...")

            # Make API request
            api_response = self._make_api_request(sec_uid, username)
            if not api_response:
                logger.error(f"❌ API ERROR: Failed to get data from TikTok API for @{username} - API request failed")
                return False, []

            # Parse post dates
            post_dates = self._parse_post_dates(api_response, username)

            if not post_dates:
                logger.info(f"✅ @{username}: API successful but no posts found in account")
                return False, []

            # Check if any posts were made on the target date
            target_posts = []

            for post_date in post_dates:
                if post_date.date() == target_date:
                    target_posts.append(post_date)

            if target_posts:
                logger.info(f"@{username}: Found {len(target_posts)} post(s) from {target_date.strftime('%Y-%m-%d')}:")
                for post_date in target_posts:
                    logger.info(f"  - Post at: {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                return True, target_posts
            else:
                logger.info(f"✅ @{username}: API successful but no posts found from {target_date.strftime('%Y-%m-%d')}")
                # Show most recent posts for debugging
                if post_dates:
                    logger.info("Most recent posts:")
                    for post_date in sorted(post_dates, reverse=True)[:3]:
                        logger.info(f"  - {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                return False, []

        except Exception as e:
            logger.error(f"❌ SYSTEM ERROR: Error checking TikTok posts for @{username} on {target_date.strftime('%Y-%m-%d')}: {e}")
            return False, []

    def check_all_accounts_for_date(self, target_date: date) -> Dict[str, Any]:
        """Check all TikTok accounts for posts on a specific date."""
        results = {
            'accounts_checked': [],
            'accounts_with_posts': [],
            'accounts_without_posts': [],
            'total_posts_today': 0,  # Keep same key name for compatibility
            'summary': {}
        }

        date_str = target_date.strftime('%Y-%m-%d')
        logger.info(f"Starting multi-account TikTok check for {date_str}...")
        logger.info(f"Checking {len(self.config.TIKTOK_ACCOUNTS)} accounts")

        for account in self.config.TIKTOK_ACCOUNTS:
            username = account['username']
            has_posts, target_posts = self.check_account_posts_for_date(account, target_date)

            account_result = {
                'username': username,
                'has_posts_today': has_posts,  # Keep same key name for compatibility
                'posts_count': len(target_posts),
                'post_times': [post.strftime('%H:%M:%S') for post in target_posts]
            }

            results['accounts_checked'].append(account_result)
            results['summary'][username] = account_result

            if has_posts:
                results['accounts_with_posts'].append(username)
                results['total_posts_today'] += len(target_posts)
            else:
                results['accounts_without_posts'].append(username)

        # Log summary
        logger.info(f"Multi-account check completed for {date_str}:")
        logger.info(f"  Accounts with posts on {date_str}: {len(results['accounts_with_posts'])}")
        logger.info(f"  Accounts without posts on {date_str}: {len(results['accounts_without_posts'])}")
        logger.info(f"  Total posts found on {date_str}: {results['total_posts_today']}")

        return results

    def check_account_posts_for_time_window(self, account: Dict[str, str], start_time: datetime, end_time: datetime) -> Tuple[bool, List[datetime]]:
        """Check if a TikTok account has posts within a specific time window."""
        username = account['username']
        sec_uid = account['secUid']
        logger.info(f"Checking posts for @{username} in time window {start_time} to {end_time}...")

        try:
            # Make API request
            api_response = self._make_api_request(sec_uid, username)
            if not api_response:
                logger.error(f"❌ API ERROR: Failed to get data from TikTok API for @{username} - API request failed")
                return False, []

            # Parse post dates
            post_dates = self._parse_post_dates(api_response, username)

            if not post_dates:
                logger.info(f"✅ @{username}: API successful but no posts found in account")
                return False, []

            # Filter posts within the time window
            # Convert timezone-aware start/end times to naive for comparison
            if start_time.tzinfo:
                start_naive = start_time.replace(tzinfo=None)
                end_naive = end_time.replace(tzinfo=None)
            else:
                start_naive = start_time
                end_naive = end_time

            window_posts = []
            for post_date in post_dates:
                # Ensure post_date is naive for comparison
                if post_date.tzinfo:
                    post_naive = post_date.replace(tzinfo=None)
                else:
                    post_naive = post_date

                if start_naive <= post_naive <= end_naive:
                    window_posts.append(post_date)

            if window_posts:
                logger.info(f"@{username}: Found {len(window_posts)} post(s) in time window:")
                for post_date in window_posts:
                    logger.info(f"  - Post at: {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                return True, window_posts
            else:
                logger.info(f"✅ @{username}: API successful but no posts found in time window")
                # Show most recent posts for debugging
                if post_dates:
                    logger.info("Most recent posts:")
                    for post_date in sorted(post_dates, reverse=True)[:3]:
                        logger.info(f"  - {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                return False, []

        except Exception as e:
            logger.error(f"❌ SYSTEM ERROR: Error checking TikTok posts for @{username} in time window: {e}")
            return False, []

    def check_all_accounts_for_time_window(self, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """Check all TikTok accounts for posts within a specific time window."""
        results = {
            'accounts_checked': [],
            'accounts_with_posts': [],
            'accounts_without_posts': [],
            'total_posts_today': 0,  # Keep same key name for compatibility
            'summary': {}
        }

        logger.info(f"Starting multi-account TikTok check for time window {start_time} to {end_time}...")
        logger.info(f"Checking {len(self.config.TIKTOK_ACCOUNTS)} accounts")

        for account in self.config.TIKTOK_ACCOUNTS:
            username = account['username']
            has_posts, window_posts = self.check_account_posts_for_time_window(account, start_time, end_time)

            account_result = {
                'username': username,
                'has_posts_today': has_posts,  # Keep same key name for compatibility
                'posts_count': len(window_posts),
                'post_times': [post.strftime('%H:%M:%S') for post in window_posts]
            }

            results['accounts_checked'].append(account_result)
            results['summary'][username] = account_result

            if has_posts:
                results['accounts_with_posts'].append(username)
                results['total_posts_today'] += len(window_posts)
            else:
                results['accounts_without_posts'].append(username)

        # Log summary
        logger.info(f"Multi-account check completed for time window:")
        logger.info(f"  Accounts with posts in window: {len(results['accounts_with_posts'])}")
        logger.info(f"  Accounts without posts in window: {len(results['accounts_without_posts'])}")
        logger.info(f"  Total posts found in window: {results['total_posts_today']}")

        return results

    def get_status_message(self, results: Dict[str, Any]) -> str:
        """Generate status message for all accounts."""
        date_str = datetime.now().strftime("%Y-%m-%d")

        if results['accounts_with_posts']:
            emoji = self.config.SUCCESS_EMOJI
            total_posts = results['total_posts_today']

            # Create detailed status for each account
            account_lines = []
            for account_data in results['accounts_checked']:
                username = account_data['username']
                if account_data['has_posts_today']:
                    post_count = account_data['posts_count']
                    post_times = account_data['post_times']
                    times_str = ', '.join(post_times) if len(post_times) <= 3 else f"{', '.join(post_times[:3])}..."
                    account_lines.append(f"@{username}: {post_count} post{'s' if post_count != 1 else ''} ({times_str})")

            if len(account_lines) == 1:
                status = f"{account_lines[0]}"
            else:
                status = f"Multiple accounts posted today:\n" + '\n'.join(account_lines)
                status += f"\nTotal: {total_posts} posts"
        else:
            emoji = self.config.FAILURE_EMOJI
            account_lines = []
            for account_data in results['accounts_checked']:
                username = account_data['username']
                account_lines.append(f"@{username}: No posts today")

            if len(account_lines) == 1:
                status = account_lines[0]
            else:
                status = '\n'.join(account_lines)

        return f"{emoji} {status} ({date_str})"
