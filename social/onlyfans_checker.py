"""OnlyFans deal checker to verify promotional offers."""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
import time
from typing import Optional, Dict, Any
from datetime import datetime

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

from core import config

logger = logging.getLogger(__name__)

class OnlyFansChecker:
    """Check for promotional deals on OnlyFans pages."""
    
    def __init__(self):
        self.config = config
        self.driver = None
    
    def _setup_driver(self) -> webdriver.Chrome:
        """Set up Chrome WebDriver with appropriate options for OnlyFans."""
        chrome_options = Options()
        
        if self.config.HEADLESS_BROWSER:
            chrome_options.add_argument("--headless=new")
        
        # Essential Chrome options for running in containers/servers
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        # Additional options that may help with OnlyFans
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            driver_path = ChromeDriverManager().install()
            logger.info(f"Initial Chrome driver path: {driver_path}")
            
            # Check if the path points to the actual executable
            import os
            import stat
            
            logger.info(f"File exists: {os.path.exists(driver_path)}")
            logger.info(f"File executable: {os.access(driver_path, os.X_OK)}")
            
            # The WebDriverManager sometimes points to the wrong file. Let's always search for the real chromedriver
            logger.info("Searching for correct chromedriver binary...")
            driver_dir = os.path.dirname(driver_path)
            found_driver = False
            
            # Look for chromedriver in the directory and subdirectories
            for root, dirs, files in os.walk(driver_dir):
                for file in files:
                    if file == 'chromedriver':
                        potential_path = os.path.join(root, file)
                        logger.info(f"Found potential chromedriver: {potential_path}")
                        if os.access(potential_path, os.X_OK):
                            driver_path = potential_path
                            found_driver = True
                            logger.info(f"Using executable chromedriver: {driver_path}")
                            break
                        else:
                            # Try to make it executable
                            try:
                                os.chmod(potential_path, 0o755)
                                if os.access(potential_path, os.X_OK):
                                    driver_path = potential_path
                                    found_driver = True
                                    logger.info(f"Made executable and using: {driver_path}")
                                    break
                            except:
                                pass
                if found_driver:
                    break
            
            if not found_driver:
                logger.error("Could not find executable chromedriver")
                raise Exception("Chromedriver not found or not executable")
            
            # Make sure the file is executable
            if os.path.exists(driver_path):
                st = os.stat(driver_path)
                if not (st.st_mode & stat.S_IEXEC):
                    os.chmod(driver_path, st.st_mode | stat.S_IEXEC)
            
            service = Service(driver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.set_page_load_timeout(30)
            
            # Execute script to remove webdriver property
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return driver
        except Exception as e:
            logger.error(f"Failed to setup Chrome driver: {e}")
            raise
    
    def check_deal_status(self, username: str = "foxnaomi") -> Dict[str, Any]:
        """Check if there's a promotional deal running on the specified OnlyFans page."""
        url = f"https://onlyfans.com/{username}"
        
        try:
            logger.info(f"Checking OnlyFans deals for @{username}...")
            self.driver = self._setup_driver()
            
            self.driver.get(url)
            logger.info(f"Navigating to {url}")
            
            # Wait for page to load
            time.sleep(5)
            
            # Check for various deal indicators
            deal_info = {
                'has_deal': False,
                'deal_percentage': None,
                'deal_text': None,
                'deal_type': None,
                'checked_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # Look for deal indicators - OnlyFans typically shows deals in various places
            deal_selectors = [
                # Common deal text patterns
                '*[contains(text(), "% OFF")]',
                '*[contains(text(), "% off")]',
                '*[contains(text(), "OFF")]',
                '*[contains(text(), "SALE")]',
                '*[contains(text(), "Deal")]',
                '*[contains(text(), "Discount")]',
                '*[contains(text(), "Special")]',
                '*[contains(text(), "Limited")]',
                # Common CSS classes/IDs that might indicate deals
                '.promotion',
                '.deal',
                '.discount',
                '.sale',
                '.special-offer',
                '[data-testid*="deal"]',
                '[data-testid*="promotion"]',
                '[data-testid*="discount"]',
                # Price-related elements that might show deals
                '.price',
                '.cost',
                '.subscription-price',
                '[class*="price"]',
                '[class*="cost"]'
            ]
            
            found_deals = []
            
            for selector in deal_selectors:
                try:
                    if selector.startswith('*[contains'):
                        # XPath selector
                        elements = self.driver.find_elements(By.XPATH, f"//{selector}")
                    else:
                        # CSS selector
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        try:
                            text = element.text.strip()
                            if text and any(keyword in text.upper() for keyword in ['OFF', 'SALE', 'DEAL', 'DISCOUNT', '%']):
                                found_deals.append({
                                    'element': element,
                                    'text': text,
                                    'selector': selector
                                })
                                logger.info(f"Found potential deal text: '{text}' using selector: {selector}")
                        except Exception as e:
                            continue
                except Exception as e:
                    continue
            
            # Process found deals to extract percentage and details
            if found_deals:
                logger.info(f"Found {len(found_deals)} potential deal indicators")
                
                # Look for percentage patterns and find the highest percentage
                import re
                highest_percentage = 0
                best_deal = None
                
                for deal in found_deals:
                    text = deal['text']
                    
                    # Look for percentage patterns like "50% OFF", "25% off", etc.
                    percentage_match = re.search(r'(\d+)%\s*(off|OFF)', text)
                    if percentage_match:
                        percentage = int(percentage_match.group(1))
                        if percentage > highest_percentage:
                            highest_percentage = percentage
                            best_deal = {
                                'percentage': percentage,
                                'text': text,
                                'type': 'percentage_discount'
                            }
                            logger.info(f"Found {percentage}% deal: '{text}'")
                
                if best_deal:
                    deal_info['has_deal'] = True
                    deal_info['deal_percentage'] = best_deal['percentage']
                    deal_info['deal_text'] = best_deal['text']
                    deal_info['deal_type'] = best_deal['type']
                    logger.info(f"Using best deal: {best_deal['percentage']}% - '{best_deal['text']}'")
                
                # If no percentage found, check for other deal indicators
                if not deal_info['has_deal']:
                    for deal in found_deals:
                        text = deal['text']
                        if any(keyword in text.upper() for keyword in ['SALE', 'DEAL', 'SPECIAL', 'LIMITED']):
                            deal_info['has_deal'] = True
                            deal_info['deal_text'] = text
                            deal_info['deal_type'] = 'general_promotion'
                            logger.info(f"Found general promotion: '{text}'")
                            break
            
            # Also check page title and meta tags for deal information
            try:
                page_title = self.driver.title
                if any(keyword in page_title.upper() for keyword in ['SALE', 'DEAL', 'OFF', 'DISCOUNT']):
                    if not deal_info['has_deal']:
                        deal_info['has_deal'] = True
                        deal_info['deal_text'] = page_title
                        deal_info['deal_type'] = 'title_promotion'
                        logger.info(f"Found deal in page title: '{page_title}'")
            except:
                pass
            
            if deal_info['has_deal']:
                logger.info(f"✅ Deal detected on @{username}: {deal_info['deal_text']}")
            else:
                logger.info(f"❌ No deals found on @{username}")
            
            return deal_info
            
        except Exception as e:
            logger.error(f"Error checking OnlyFans deals: {e}")
            return {
                'has_deal': False,
                'error': str(e),
                'checked_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        finally:
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
    
    def get_deal_status_message(self, deal_info: Dict[str, Any], username: str = "foxnaomi") -> str:
        """Generate a status message for the deal check."""
        if deal_info.get('error'):
            return f"❌ @{username}: Deal check failed - {deal_info['error']}"
        
        if deal_info['has_deal']:
            if deal_info.get('deal_percentage'):
                return f"🔥 @{username}: {deal_info['deal_percentage']}% OFF deal active!"
            elif deal_info.get('deal_text'):
                return f"🔥 @{username}: Deal active - {deal_info['deal_text']}"
            else:
                return f"🔥 @{username}: Promotional deal detected!"
        else:
            return f"💰 @{username}: No current deals"

def main():
    """Test function to check OnlyFans deals."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    checker = OnlyFansChecker()
    deal_info = checker.check_deal_status("foxnaomi")
    message = checker.get_deal_status_message(deal_info, "foxnaomi")
    
    print(f"\nDeal Check Result:")
    print(f"Deal Info: {deal_info}")
    print(f"Status Message: {message}")
    
    return deal_info['has_deal']

if __name__ == "__main__":
    main()