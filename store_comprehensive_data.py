#!/usr/bin/env python3
"""
Comprehensive data storage system for OnlyMonster + Social Media
Handles 24-hour tracking with proper data management and deduplication
"""
import sys
import os
from datetime import datetime, timedelta
from core.database import TrackingDatabase
from core.onlymonster_scraper import Only<PERSON>onsterScraper
from social.multi_platform_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get_24_hour_window

class ComprehensiveDataCollector:
    def __init__(self):
        self.db = TrackingDatabase()
    
    def get_24_hour_timestamp_range(self):
        """Get start and end timestamps for accurate 24-hour tracking"""
        now = datetime.now()
        
        # Use same 24-hour window as social media (6 AM to 6 AM)
        if now.hour >= 6:
            # After 6 AM - window is yesterday 6 AM to today 6 AM
            end_time = now.replace(hour=6, minute=0, second=0, microsecond=0)
            start_time = end_time - timedelta(hours=24)
        else:
            # Before 6 AM - window is day before yesterday 6 AM to yesterday 6 AM
            today_6am = now.replace(hour=6, minute=0, second=0, microsecond=0)
            end_time = today_6am - timedelta(days=1)
            start_time = end_time - timedelta(hours=24)
        
        return start_time, end_time
    
    def collect_onlymonster_data(self):
        """Collect OnlyMonster tracking data"""
        print("🎯 Collecting OnlyMonster data...")
        
        try:
            scraper = OnlyMonsterScraper()
            tracking_data = scraper.run_scraping()
            
            if tracking_data:
                print(f"✅ Collected {len(tracking_data)} OnlyMonster tracking links")
                return True
            else:
                print("❌ No OnlyMonster data collected")
                return False
                
        except Exception as e:
            print(f"❌ OnlyMonster scraping failed: {e}")
            return False
    
    def collect_social_media_data(self):
        """Collect social media data using 24-hour window"""
        print("📱 Collecting social media data...")
        
        try:
            # Get 24-hour window for consistency
            start_time, end_time = get_24_hour_window()
            print(f"   Using window: {start_time.strftime('%Y-%m-%d %H:%M')} to {end_time.strftime('%Y-%m-%d %H:%M')}")
            
            # Check all platforms
            checker = MultiPlatformChecker(time_window=(start_time, end_time))
            results = checker.check_all_platforms()
            
            # Store social media data
            check_date = datetime.now().strftime('%Y-%m-%d')
            social_data = {'results': results}
            
            records_inserted = self.db.insert_social_media_history(social_data, check_date)
            print(f"✅ Stored {records_inserted} social media records")
            
            return results
            
        except Exception as e:
            print(f"❌ Social media collection failed: {e}")
            return None
    
    def has_onlymonster_data_changed(self, tolerance_hours=1):
        """Check if OnlyMonster data has changed significantly in the last period"""
        try:
            # Get latest data
            latest_data = self.db.get_latest_data(limit=50)  # Get recent data
            
            if not latest_data:
                return True  # No data, definitely need to collect
            
            # Check if we have recent data (within tolerance)
            latest_timestamp_str = latest_data[0][4]  # timestamp is 5th column
            latest_timestamp = datetime.fromisoformat(latest_timestamp_str)
            time_since_last = datetime.now() - latest_timestamp
            
            if time_since_last > timedelta(hours=tolerance_hours):
                print(f"⏰ Last OnlyMonster data is {time_since_last} old (>{tolerance_hours}h), collecting new data")
                return True
            
            print(f"✅ OnlyMonster data is recent ({time_since_last} ago), checking for significant changes...")
            
            # Get data from same time yesterday for comparison
            yesterday = datetime.now() - timedelta(days=1)
            yesterday_start = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
            yesterday_end = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
            
            yesterday_data = self.db.get_data_by_date_range(
                yesterday_start.isoformat(), 
                yesterday_end.isoformat()
            )
            
            # If no yesterday data, collect new data
            if not yesterday_data:
                print("📊 No comparison data available, collecting new data")
                return True
            
            # Simple change detection - if we have very recent data, skip collection
            if time_since_last < timedelta(minutes=30):
                print("⚡ Very recent data available, skipping OnlyMonster collection")
                return False
            
            return True
            
        except Exception as e:
            print(f"⚠️ Error checking OnlyMonster data changes: {e}")
            return True  # If we can't check, assume we need new data
    
    def cleanup_old_data(self, days_to_keep=90):
        """Clean up old data from both tables"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                # Cleanup OnlyMonster data
                cursor.execute(
                    'SELECT COUNT(*) FROM tracking_data WHERE timestamp < ?',
                    (cutoff_date.isoformat(),)
                )
                onlymonster_count = cursor.fetchone()[0]
                
                if onlymonster_count > 0:
                    cursor.execute(
                        'DELETE FROM tracking_data WHERE timestamp < ?',
                        (cutoff_date.isoformat(),)
                    )
                    print(f"🗑️ Cleaned up {onlymonster_count} old OnlyMonster records")
                
                # Cleanup social media data
                cutoff_date_str = cutoff_date.strftime('%Y-%m-%d')
                cursor.execute(
                    'SELECT COUNT(*) FROM social_media_history WHERE check_date < ?',
                    (cutoff_date_str,)
                )
                social_count = cursor.fetchone()[0]
                
                if social_count > 0:
                    cursor.execute(
                        'DELETE FROM social_media_history WHERE check_date < ?',
                        (cutoff_date_str,)
                    )
                    print(f"🗑️ Cleaned up {social_count} old social media records")
                
                conn.commit()
                
                if onlymonster_count == 0 and social_count == 0:
                    print(f"✅ No cleanup needed (no data older than {days_to_keep} days)")
                    
        except Exception as e:
            print(f"⚠️ Error during cleanup: {e}")
    
    def get_collection_summary(self):
        """Get summary of collected data for the 24-hour period"""
        try:
            start_time, end_time = self.get_24_hour_timestamp_range()
            
            # Get OnlyMonster data summary
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                # OnlyMonster metrics for 24-hour period
                cursor.execute('''
                    SELECT COUNT(*) as link_count,
                           SUM(clicks) as total_clicks,
                           SUM(fans) as total_fans,
                           SUM(earnings) as total_earnings
                    FROM tracking_data 
                    WHERE timestamp >= ? AND timestamp <= ?
                    ORDER BY timestamp DESC LIMIT 1
                ''', (start_time.isoformat(), end_time.isoformat()))
                
                om_result = cursor.fetchone()
                
                # Social media summary for today
                today = datetime.now().strftime('%Y-%m-%d')
                social_data = self.db.get_social_media_history(date=today)
                
                social_summary = {
                    'tiktok_accounts': 0, 'instagram_accounts': 0, 'reddit_accounts': 0,
                    'tiktok_posts': 0, 'instagram_posts': 0, 'reddit_posts': 0,
                    'total_accounts': len(social_data) if social_data else 0
                }
                
                if social_data:
                    for platform, username, post_count, removed_posts, post_times, removal_reasons, status, error_message, check_date in social_data:
                        if platform == 'tiktok':
                            social_summary['tiktok_accounts'] += 1
                            social_summary['tiktok_posts'] += post_count or 0
                        elif platform == 'instagram':
                            social_summary['instagram_accounts'] += 1  
                            social_summary['instagram_posts'] += post_count or 0
                        elif platform == 'reddit':
                            social_summary['reddit_accounts'] += 1
                            social_summary['reddit_posts'] += post_count or 0
                
                return {
                    'onlymonster': {
                        'links': om_result[0] if om_result else 0,
                        'clicks': om_result[1] if om_result else 0,
                        'fans': om_result[2] if om_result else 0,
                        'earnings': float(om_result[3]) if om_result and om_result[3] else 0.0
                    },
                    'social_media': social_summary,
                    'time_window': f"{start_time.strftime('%Y-%m-%d %H:%M')} to {end_time.strftime('%Y-%m-%d %H:%M')}"
                }
                
        except Exception as e:
            print(f"⚠️ Error getting collection summary: {e}")
            return None

def main():
    """Main function to collect all data comprehensively"""
    print("🚀 Starting comprehensive data collection...")
    print(f"⏰ Timestamp: {datetime.now()}")
    
    collector = ComprehensiveDataCollector()
    
    # Check for force flag
    force_update = "--force" in sys.argv or "-f" in sys.argv
    if force_update:
        print("⚡ Force update mode - collecting all data regardless of changes")
    
    # Collect OnlyMonster data (with smart checking unless forced)
    if force_update or collector.has_onlymonster_data_changed():
        onlymonster_success = collector.collect_onlymonster_data()
    else:
        print("⏭️ Skipping OnlyMonster collection - no significant changes needed")
        onlymonster_success = True
    
    # Always collect social media data (it has its own change detection)
    social_results = collector.collect_social_media_data()
    
    # Run cleanup periodically (every 7th day of month)
    if datetime.now().day % 7 == 0:
        print("\n🧹 Running periodic cleanup...")
        collector.cleanup_old_data()
    
    # Get and display summary
    summary = collector.get_collection_summary()
    if summary:
        print("\n=== 📊 24-Hour Data Collection Summary ===")
        print(f"⏰ Period: {summary['time_window']}")
        print("\n🎯 OnlyMonster:")
        om = summary['onlymonster']
        print(f"  📈 Links: {om['links']}, Clicks: {om['clicks']}, Fans: {om['fans']}, Earnings: ${om['earnings']:.2f}")
        
        print("\n📱 Social Media:")
        sm = summary['social_media'] 
        print(f"  📱 TikTok: {sm['tiktok_accounts']} accounts, {sm['tiktok_posts']} posts")
        print(f"  📸 Instagram: {sm['instagram_accounts']} accounts, {sm['instagram_posts']} posts")
        print(f"  💬 Reddit: {sm['reddit_accounts']} accounts, {sm['reddit_posts']} posts")
        print(f"  📊 Total: {sm['total_accounts']} accounts monitored")
    
    success = onlymonster_success and (social_results is not None)
    if success:
        print("\n✅ Comprehensive data collection completed successfully!")
    else:
        print("\n⚠️ Some data collection issues occurred")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)