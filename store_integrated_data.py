#!/usr/bin/env python3
"""
Lightweight integrated data storage for OnlyMonster + Social Media
Optimized for production use with proper 24-hour tracking and minimal database bloat
"""
import sys
import os
from datetime import datetime, timedelta
from core.database import TrackingDatabase
from social.multi_platform_checker import <PERSON><PERSON>lat<PERSON><PERSON><PERSON><PERSON>, get_24_hour_window

def update_social_media_data():
    """Update social media data with change detection"""
    print("📱 Updating social media data...")
    
    try:
        db = TrackingDatabase()
        
        # Get 24-hour window
        start_time, end_time = get_24_hour_window()
        print(f"   📅 Time window: {start_time.strftime('%Y-%m-%d %H:%M')} to {end_time.strftime('%Y-%m-%d %H:%M')}")
        
        # Run social media check
        checker = MultiPlatformChecker(time_window=(start_time, end_time))
        results = checker.check_all_platforms()
        
        check_date = datetime.now().strftime('%Y-%m-%d')
        social_data = {'results': results}
        
        # Replace today's data (prevents accumulation)
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM social_media_history WHERE check_date = ?', (check_date,))
            conn.commit()
        
        # Store new data
        records_inserted = db.insert_social_media_history(social_data, check_date)
        
        summary = results.get('summary', {})
        print(f"   ✅ Social: {records_inserted} records stored")
        print(f"      📱 TikTok: {summary.get('tiktok_posts_today', 0)} posts")
        print(f"      📸 Instagram: {summary.get('instagram_posts_today', 0)} posts")
        print(f"      💬 Reddit: {summary.get('reddit_posts_today', 0)} posts")
        print(f"      📊 Total: {summary.get('total_posts_today', 0)} posts across {summary.get('total_accounts_with_posts', 0)}/{summary.get('total_accounts_checked', 0)} accounts")
        
        # Also store OnlyFans deal status for persistence (best-effort)
        try:
            from social.onlyfans_checker import OnlyFansChecker
            ofc = OnlyFansChecker()
            deal = ofc.check_deal_status("foxnaomi")
            db.upsert_onlyfans_deal("foxnaomi", deal)
            print(f"   ✨ Stored OnlyFans deal: {deal}")
        except Exception as e:
            print(f"   ⚠️ OnlyFans deal check failed (skipping): {e}")

        return results
        
    except Exception as e:
        print(f"❌ Social media collection failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def should_update_onlymonster(db, hours_threshold=2):
    """Check if OnlyMonster data should be updated based on recency"""
    try:
        # Get most recent OnlyMonster data
        latest_data = db.get_latest_data(limit=1)
        
        if not latest_data:
            print("   📊 No OnlyMonster data found, update needed")
            return True
        
        # Check timestamp
        latest_timestamp_str = latest_data[0][4]  # timestamp column
        latest_timestamp = datetime.fromisoformat(latest_timestamp_str)
        time_since_last = datetime.now() - latest_timestamp
        
        if time_since_last > timedelta(hours=hours_threshold):
            print(f"   ⏰ OnlyMonster data is {time_since_last} old (>{hours_threshold}h), update needed")
            return True
        else:
            print(f"   ✅ OnlyMonster data is recent ({time_since_last} ago), skipping")
            return False
            
    except Exception as e:
        print(f"   ⚠️ Error checking OnlyMonster data recency: {e}")
        return True  # If we can't check, assume update needed

def update_onlymonster_data(force: bool = False, retries: int = 2):
    """Update OnlyMonster data (lightweight - reuse existing data if recent)"""
    print("🎯 Checking OnlyMonster data...")
    
    try:
        db = TrackingDatabase()
        
        # Check if update is needed
        if not force and not should_update_onlymonster(db):
            return True  # Recent data exists, consider this a success
        
        print("   🔄 Running OnlyMonster scraper...")
        
        # Import and run scraper
        from core.onlymonster_scraper import OnlyMonsterScraper
        
        attempt = 0
        while attempt <= retries:
            attempt += 1
            try:
                print(f"   🔁 OnlyMonster scrape attempt {attempt}/{retries+1}...")
                scraper = OnlyMonsterScraper()
                tracking_data = scraper.run_scraping()
                if tracking_data:
                    print(f"   ✅ OnlyMonster: {len(tracking_data)} tracking links updated")
                    return True
                else:
                    print("   ⚠️ No OnlyMonster data collected (empty result)")
            except Exception as e:
                print(f"   ❌ Attempt {attempt} failed: {e}")
            # brief backoff between attempts
            if attempt <= retries:
                from time import sleep
                sleep(5)
        return False
            
    except Exception as e:
        print(f"   ❌ OnlyMonster update failed: {e}")
        # Don't crash the whole process if OnlyMonster fails
        print("   📱 Continuing with social media data only...")
        return False

def cleanup_old_data(db, om_days=90, social_days=90):
    """Efficient cleanup of old data"""
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # Cleanup OnlyMonster data older than X days
            om_cutoff = (datetime.now() - timedelta(days=om_days)).isoformat()
            cursor.execute('SELECT COUNT(*) FROM tracking_data WHERE timestamp < ?', (om_cutoff,))
            om_count = cursor.fetchone()[0]
            
            if om_count > 0:
                cursor.execute('DELETE FROM tracking_data WHERE timestamp < ?', (om_cutoff,))
                print(f"   🗑️ Cleaned {om_count} old OnlyMonster records (>{om_days} days)")
            
            # Cleanup social media data older than X days  
            social_cutoff = (datetime.now() - timedelta(days=social_days)).strftime('%Y-%m-%d')
            cursor.execute('SELECT COUNT(*) FROM social_media_history WHERE check_date < ?', (social_cutoff,))
            social_count = cursor.fetchone()[0]
            
            if social_count > 0:
                cursor.execute('DELETE FROM social_media_history WHERE check_date < ?', (social_cutoff,))
                print(f"   🗑️ Cleaned {social_count} old social records (>{social_days} days)")
            
            conn.commit()
            
            if om_count == 0 and social_count == 0:
                print("   ✅ No cleanup needed")
                
    except Exception as e:
        print(f"   ⚠️ Cleanup error: {e}")

def get_data_summary():
    """Get a quick summary of stored data"""
    try:
        db = TrackingDatabase()
        
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # OnlyMonster summary
            cursor.execute('SELECT COUNT(DISTINCT tracking_link_name) FROM tracking_data WHERE timestamp > ?', 
                         ((datetime.now() - timedelta(hours=24)).isoformat(),))
            om_links = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM tracking_data WHERE timestamp > ?', 
                         ((datetime.now() - timedelta(hours=24)).isoformat(),))
            om_records = cursor.fetchone()[0]
            
            # Social media summary
            today = datetime.now().strftime('%Y-%m-%d')
            cursor.execute('SELECT COUNT(*) FROM social_media_history WHERE check_date = ?', (today,))
            social_records = cursor.fetchone()[0]
            
            cursor.execute('SELECT SUM(post_count) FROM social_media_history WHERE check_date = ? AND post_count > 0', (today,))
            total_posts = cursor.fetchone()[0] or 0
            
            return {
                'onlymonster_links': om_links,
                'onlymonster_records': om_records,
                'social_records': social_records,
                'total_social_posts': total_posts
            }
    except Exception as e:
        print(f"Error getting summary: {e}")
        return None

def main():
    """Main execution function"""
    print("🚀 Integrated Data Collection Started")
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Force flag check
    force_onlymonster = "--force" in sys.argv or "-f" in sys.argv
    if force_onlymonster:
        print("⚡ Force mode: Will update OnlyMonster regardless of recency")
    
    db = TrackingDatabase()
    
    # Update OnlyMonster data (with smart timing unless forced)
    if force_onlymonster:
        # Override the smart checking
        om_success = update_onlymonster_data(force=True)
    else:
        om_success = update_onlymonster_data(force=False)
    
    # Always update social media data (fast and has its own optimization)
    social_success = update_social_media_data()
    
    # Periodic cleanup (weekly)
    if datetime.now().day % 7 == 0:
        print("\n🧹 Running weekly cleanup...")
        cleanup_old_data(db)
    
    # Summary
    print("\n" + "="*50)
    summary = get_data_summary()
    if summary:
        print("📊 DATA SUMMARY (Last 24 Hours):")
        print(f"   🎯 OnlyMonster: {summary['onlymonster_links']} links, {summary['onlymonster_records']} records")
        print(f"   📱 Social Media: {summary['social_records']} account records, {summary['total_social_posts']} posts")
    else:
        print("📊 Summary unavailable")
    
    overall_success = social_success is not None  # Social media is critical
    status = "✅ SUCCESS" if overall_success else "⚠️  PARTIAL"
    print(f"{status} - Data collection completed")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
