#!/usr/bin/env python3
"""
Store social media data in the database for historical tracking
"""
import sys
import os
from datetime import datetime
from core.database import TrackingDatabase
from social.multi_platform_checker import MultiPlatform<PERSON>he<PERSON>, get_24_hour_window

def store_current_social_data():
    """Run social media check and store results in database"""
    try:
        # Initialize database
        db = TrackingDatabase()
        
        # Get 24-hour window
        start_time, end_time = get_24_hour_window()
        print(f"Checking social media activity from {start_time} to {end_time}")
        
        # Run social media check
        checker = MultiPlatformChecker(time_window=(start_time, end_time))
        results = checker.check_all_platforms()
        
        print("Social media check completed successfully!")
        print(f"Summary: {results['summary']}")
        
        # Store in database
        check_date = datetime.now().strftime('%Y-%m-%d')
        social_data = {'results': results}
        
        records_inserted = db.insert_social_media_history(social_data, check_date)
        print(f"Stored {records_inserted} records in social media history for {check_date}")
        
        return results
        
    except Exception as e:
        print(f"Error storing social data: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("Running social media checker and storing results...")
    results = store_current_social_data()
    
    if results:
        print("\n=== Social Media Activity Summary ===")
        summary = results.get('summary', {})
        print(f"Total posts today: {summary.get('total_posts_today', 0)}")
        print(f"Total accounts with posts: {summary.get('total_accounts_with_posts', 0)}")
        print(f"TikTok posts: {summary.get('tiktok_posts_today', 0)}")  
        print(f"Instagram posts: {summary.get('instagram_posts_today', 0)}")
        print(f"Reddit posts: {summary.get('reddit_posts_today', 0)}")
        
        if 'reddit' in results:
            print(f"Reddit removed posts: {results['reddit'].get('total_removed_posts', 0)}")
        
        print("\nData successfully stored in database!")
    else:
        print("Failed to check social media activity")