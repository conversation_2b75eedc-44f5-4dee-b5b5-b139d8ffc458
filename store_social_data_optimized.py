#!/usr/bin/env python3
"""
Optimized social media data storage with smart deduplication and compression
"""
import sys
import os
from datetime import datetime, timedelta
from core.database import TrackingDatabase
from social.multi_platform_checker import <PERSON><PERSON>lat<PERSON><PERSON><PERSON><PERSON>, get_24_hour_window

def has_data_changed(db, new_data, check_date):
    """Check if data has meaningfully changed since last check today"""
    try:
        # Get latest data for today
        existing_data = db.get_social_media_history(date=check_date)
        
        if not existing_data:
            return True  # No previous data, definitely changed
        
        # Create lookup of existing data
        existing_lookup = {}
        for platform, username, post_count, removed_posts, post_times, removal_reasons, status, error_message, date in existing_data:
            key = f"{platform}:{username}"
            existing_lookup[key] = {
                'post_count': post_count or 0,
                'removed_posts': removed_posts or 0,
                'status': status or 'inactive'
            }
        
        # Check new data against existing
        results = new_data['results']
        
        # Check TikTok changes
        for username, summary in results.get('tiktok', {}).get('summary', {}).items():
            key = f"tiktok:{username}"
            new_posts = summary.get('posts_count', 0)
            if key not in existing_lookup or existing_lookup[key]['post_count'] != new_posts:
                print(f"📱 TikTok change detected: @{username} posts {existing_lookup.get(key, {}).get('post_count', 0)} → {new_posts}")
                return True
        
        # Check Instagram changes
        for username, details in results.get('instagram', {}).get('account_details', {}).items():
            key = f"instagram:{username}"
            new_posts = details.get('post_count', 0)
            if key not in existing_lookup or existing_lookup[key]['post_count'] != new_posts:
                print(f"📸 Instagram change detected: @{username} posts {existing_lookup.get(key, {}).get('post_count', 0)} → {new_posts}")
                return True
        
        # Check Reddit changes  
        for username, details in results.get('reddit', {}).get('account_details', {}).items():
            key = f"reddit:{username}"
            new_posts = details.get('post_count', 0)
            new_removed = details.get('removed_count', 0)
            existing = existing_lookup.get(key, {'post_count': 0, 'removed_posts': 0})
            if existing['post_count'] != new_posts or existing['removed_posts'] != new_removed:
                print(f"💬 Reddit change detected: {username} posts {existing['post_count']} → {new_posts}, removed {existing['removed_posts']} → {new_removed}")
                return True
        
        print("✅ No significant changes detected since last check today")
        return False
        
    except Exception as e:
        print(f"⚠️  Error checking for changes: {e}")
        return True  # If we can't check, assume changed to be safe

def cleanup_old_data(db, days_to_keep=90):
    """Clean up old detailed data to prevent database bloat"""
    try:
        cutoff_date = (datetime.now() - timedelta(days=days_to_keep)).strftime('%Y-%m-%d')
        
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # Count records to be deleted
            cursor.execute('SELECT COUNT(*) FROM social_media_history WHERE check_date < ?', (cutoff_date,))
            count_to_delete = cursor.fetchone()[0]
            
            if count_to_delete > 0:
                # Delete old records
                cursor.execute('DELETE FROM social_media_history WHERE check_date < ?', (cutoff_date,))
                conn.commit()
                print(f"🗑️  Cleaned up {count_to_delete} old records (older than {days_to_keep} days)")
            else:
                print(f"✅ No cleanup needed (no records older than {days_to_keep} days)")
                
    except Exception as e:
        print(f"⚠️  Error during cleanup: {e}")

def store_optimized_social_data(force_update=False):
    """Store social media data with optimization to prevent bloat"""
    try:
        # Initialize database
        db = TrackingDatabase()
        
        # Get 24-hour window
        start_time, end_time = get_24_hour_window()
        print(f"🔍 Checking social media activity from {start_time} to {end_time}")
        
        # Run social media check
        checker = MultiPlatformChecker(time_window=(start_time, end_time))
        results = checker.check_all_platforms()
        
        check_date = datetime.now().strftime('%Y-%m-%d')
        social_data = {'results': results}
        
        # Check if data has meaningfully changed (unless forced)
        if not force_update and not has_data_changed(db, social_data, check_date):
            print("⏭️  Skipping storage - no meaningful changes detected")
            return results
        
        print("💾 Storing updated social media data...")
        
        # Delete existing data for today before inserting new (prevents duplicates)
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM social_media_history WHERE check_date = ?', (check_date,))
            conn.commit()
            print(f"🔄 Replaced existing data for {check_date}")
        
        # Store new data
        records_inserted = db.insert_social_media_history(social_data, check_date)
        print(f"✅ Stored {records_inserted} records for {check_date}")

        # Also store OnlyFans deal status for persistence (best-effort)
        try:
            from social.onlyfans_checker import OnlyFansChecker
            ofc = OnlyFansChecker()
            deal = ofc.check_deal_status("foxnaomi")
            db.upsert_onlyfans_deal("foxnaomi", deal)
            print(f"✨ Stored OnlyFans deal: {deal}")
        except Exception as e:
            print(f"⚠️  OnlyFans deal check failed (skipping): {e}")
        
        # Run periodic cleanup (every 7th run, roughly weekly)
        if datetime.now().day % 7 == 0:
            cleanup_old_data(db)
        
        return results
        
    except Exception as e:
        print(f"❌ Error storing social data: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🚀 Running optimized social media checker...")
    
    # Check for force flag
    force_update = "--force" in sys.argv or "-f" in sys.argv
    if force_update:
        print("⚡ Force update mode - will store regardless of changes")
    
    results = store_optimized_social_data(force_update)
    
    if results:
        print("\n=== 📊 Social Media Activity Summary ===")
        summary = results.get('summary', {})
        print(f"📱 TikTok posts: {summary.get('tiktok_posts_today', 0)}")
        print(f"📸 Instagram posts: {summary.get('instagram_posts_today', 0)}")
        print(f"💬 Reddit posts: {summary.get('reddit_posts_today', 0)}")
        
        if 'reddit' in results:
            print(f"🗑️  Reddit removed posts: {results['reddit'].get('total_removed_posts', 0)}")
        
        print(f"✅ Total posts: {summary.get('total_posts_today', 0)} across {summary.get('total_accounts_with_posts', 0)}/{summary.get('total_accounts_checked', 0)} accounts")
        print("\n🎯 Optimized storage complete!")
    else:
        print("❌ Failed to check social media activity")
