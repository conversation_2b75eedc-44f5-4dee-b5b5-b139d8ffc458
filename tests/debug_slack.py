#!/usr/bin/env python3
"""
Comprehensive Slack debugging script
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
import json
from core.config import (
    SLACK_BOT_TOKEN, SLACK_APP_TOKEN, SLACK_CHANNEL_ID,
    SLACK_CLIENT_ID, SLACK_CLIENT_SECRET, SLACK_REFRESH_TOKEN
)

def test_bot_token():
    """Test the bot token authentication"""
    print("🔍 Testing Bot Token...")
    
    url = "https://slack.com/api/auth.test"
    headers = {
        "Authorization": f"Bearer {SLACK_BOT_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, headers=headers)
        result = response.json()
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if result.get("ok"):
            print("✅ Bot token is valid!")
            print(f"   Bot User: {result.get('user')}")
            print(f"   Team: {result.get('team')}")
            return True
        else:
            print(f"❌ Bot token error: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_app_token():
    """Test the app-level token"""
    print("\n🔍 Testing App-Level Token...")
    
    url = "https://slack.com/api/auth.test"
    headers = {
        "Authorization": f"Bearer {SLACK_APP_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, headers=headers)
        result = response.json()
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if result.get("ok"):
            print("✅ App token is valid!")
            return True
        else:
            print(f"❌ App token error: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_channel_access():
    """Test access to the specific channel"""
    print(f"\n🔍 Testing Channel Access ({SLACK_CHANNEL_ID})...")
    
    url = "https://slack.com/api/conversations.info"
    headers = {
        "Authorization": f"Bearer {SLACK_BOT_TOKEN}",
        "Content-Type": "application/json"
    }
    
    params = {
        "channel": SLACK_CHANNEL_ID
    }
    
    try:
        response = requests.get(url, headers=headers, params=params)
        result = response.json()
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if result.get("ok"):
            channel = result.get("channel", {})
            print("✅ Channel access successful!")
            print(f"   Channel Name: #{channel.get('name')}")
            print(f"   Channel ID: {channel.get('id')}")
            print(f"   Is Member: {channel.get('is_member')}")
            return True
        else:
            print(f"❌ Channel access error: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_send_message():
    """Test sending a simple message"""
    print(f"\n🔍 Testing Message Sending...")
    
    url = "https://slack.com/api/chat.postMessage"
    headers = {
        "Authorization": f"Bearer {SLACK_BOT_TOKEN}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "channel": SLACK_CHANNEL_ID,
        "text": "🧪 Test message from OnlyMonster automation",
        "mrkdwn": True
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        result = response.json()
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if result.get("ok"):
            print("✅ Message sent successfully!")
            print(f"   Message TS: {result.get('ts')}")
            return True
        else:
            error = result.get("error")
            print(f"❌ Message sending error: {error}")
            
            # Provide specific error guidance
            if error == "missing_scope":
                print("💡 Missing required scopes. Need: chat:write")
            elif error == "channel_not_found":
                print("💡 Channel not found. Check channel ID.")
            elif error == "not_in_channel":
                print("💡 Bot not in channel. Invite bot to channel.")
            elif error == "invalid_auth":
                print("💡 Invalid token. Check bot token.")
            
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def refresh_token():
    """Try to refresh the token"""
    print(f"\n🔍 Testing Token Refresh...")
    
    url = "https://slack.com/api/oauth.v2.access"
    
    payload = {
        "client_id": SLACK_CLIENT_ID,
        "client_secret": SLACK_CLIENT_SECRET,
        "refresh_token": SLACK_REFRESH_TOKEN,
        "grant_type": "refresh_token"
    }
    
    try:
        response = requests.post(url, data=payload)
        result = response.json()
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if result.get("ok"):
            print("✅ Token refresh successful!")
            new_token = result.get("access_token")
            print(f"   New Token: {new_token[:20]}...")
            return new_token
        else:
            print(f"❌ Token refresh error: {result.get('error')}")
            return None
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return None

def test_with_different_tokens():
    """Test with different token combinations"""
    print(f"\n🔍 Testing Different Token Types...")
    
    tokens_to_test = [
        ("Bot Token", SLACK_BOT_TOKEN),
        ("App Token", SLACK_APP_TOKEN),
        ("Refresh Token", SLACK_REFRESH_TOKEN)
    ]
    
    for token_name, token in tokens_to_test:
        print(f"\n--- Testing {token_name} ---")
        
        url = "https://slack.com/api/auth.test"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(url, headers=headers)
            result = response.json()
            
            if result.get("ok"):
                print(f"✅ {token_name} is valid")
                print(f"   User: {result.get('user', 'N/A')}")
                print(f"   Team: {result.get('team', 'N/A')}")
            else:
                print(f"❌ {token_name} error: {result.get('error')}")
                
        except Exception as e:
            print(f"❌ {token_name} request failed: {e}")

def main():
    """Run comprehensive Slack debugging"""
    print("🚀 SLACK INTEGRATION DEBUGGING")
    print("="*50)
    
    print(f"Channel ID: {SLACK_CHANNEL_ID}")
    print(f"Bot Token: {SLACK_BOT_TOKEN[:20]}...")
    print(f"App Token: {SLACK_APP_TOKEN[:20]}...")
    
    # Test sequence
    tests = [
        ("Bot Token Auth", test_bot_token),
        ("App Token Auth", test_app_token),
        ("Channel Access", test_channel_access),
        ("Send Message", test_send_message),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 TEST SUMMARY:")
    print(f"{'='*50}")
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if not results.get("Bot Token Auth"):
        print("   1. Check bot token validity")
        print("   2. Regenerate bot token if needed")
    
    if not results.get("Channel Access"):
        print("   3. Invite bot to the channel:")
        print(f"      /invite @your-bot-name in #{SLACK_CHANNEL_ID}")
    
    if not results.get("Send Message"):
        print("   4. Add required scopes:")
        print("      - chat:write")
        print("      - channels:read")
        print("   5. Reinstall app to workspace")
    
    # Try token refresh if main tests fail
    if not any(results.values()):
        print(f"\n🔄 Attempting token refresh...")
        new_token = refresh_token()
        if new_token:
            print(f"   New token available: {new_token[:20]}...")

if __name__ == "__main__":
    main()
