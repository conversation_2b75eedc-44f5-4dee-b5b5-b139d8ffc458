#!/usr/bin/env python3
"""Debug script to examine TikTok API response structure."""

import http.client
import json
from core.config import Config

def debug_tiktok_response():
    """Debug the TikTok API response structure."""
    config = Config()
    
    for account in config.TIKTOK_ACCOUNTS:
        username = account['username']
        sec_uid = account['secUid']
        
        print(f"\n=== Debugging @{username} ===")
        print(f"SecUID: {sec_uid}")
        
        try:
            conn = http.client.HTTPSConnection("tiktok-api23.p.rapidapi.com")

            headers = {
                'x-rapidapi-key': config.RAPIDAPI_KEY,
                'x-rapidapi-host': "tiktok-api23.p.rapidapi.com"
            }

            endpoint = f"/api/user/posts?secUid={sec_uid}&count=35&cursor=0"
            
            print(f"Making request to: {endpoint}")
            conn.request("GET", endpoint, headers=headers)

            res = conn.getresponse()
            data = res.read()

            print(f"Status: {res.status}")
            print(f"Response length: {len(data)} bytes")
            
            if res.status == 200:
                try:
                    response_data = json.loads(data.decode("utf-8"))
                    print("Successfully parsed JSON response")
                    print(f"Response keys: {list(response_data.keys()) if isinstance(response_data, dict) else 'Not a dict'}")
                    
                    # Check the data structure
                    if isinstance(response_data, dict):
                        for key, value in response_data.items():
                            if isinstance(value, dict):
                                print(f"  {key}: dict with keys {list(value.keys())}")
                            elif isinstance(value, list):
                                print(f"  {key}: list with {len(value)} items")
                            else:
                                print(f"  {key}: {type(value).__name__} = {value}")
                    
                    # Look for posts in the response
                    posts = []
                    if 'data' in response_data:
                        if 'itemList' in response_data['data']:
                            posts = response_data['data']['itemList']
                            print(f"\nFound {len(posts)} posts in data.itemList")
                        else:
                            print(f"\ndata keys: {list(response_data['data'].keys()) if isinstance(response_data['data'], dict) else 'data is not dict'}")
                    
                    # Check first post structure if available
                    if posts and len(posts) > 0:
                        first_post = posts[0]
                        print(f"\nFirst post type: {type(first_post)}")
                        if isinstance(first_post, dict):
                            print(f"First post keys: {list(first_post.keys())}")
                            # Look for timestamp fields
                            timestamp_fields = ['createTime', 'create_time', 'timestamp', 'publishTime', 'publish_time', 'uploadTime']
                            for field in timestamp_fields:
                                if field in first_post:
                                    print(f"  Found timestamp field '{field}': {first_post[field]} (type: {type(first_post[field])})")
                        else:
                            print(f"First post is not a dict: {first_post}")
                    
                except json.JSONDecodeError as e:
                    print(f"Failed to parse JSON: {e}")
                    print(f"Raw response: {data.decode('utf-8')[:500]}...")
            else:
                print(f"API error: {data.decode('utf-8')}")
                
        except Exception as e:
            print(f"Error: {e}")
        finally:
            try:
                conn.close()
            except:
                pass

if __name__ == "__main__":
    debug_tiktok_response()
