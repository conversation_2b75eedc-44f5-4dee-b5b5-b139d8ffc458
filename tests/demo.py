#!/usr/bin/env python3
"""Demo script to show TikTok Post Notifier functionality."""

import time
import sys
from main import setup_logging, run_check

def demo_notifications():
    """Demonstrate both success and failure notifications."""
    print("🎬 TikTok Post Notifier Demo")
    print("=" * 50)
    
    # Set up logging
    setup_logging()
    
    print("This demo will show both possible outcomes:")
    print("✅ Posts found today")
    print("❌ No posts found today")
    print()
    
    # Run multiple checks to show different outcomes
    for i in range(3):
        print(f"Demo Check #{i+1}")
        print("-" * 20)
        
        result = run_check()
        
        if result['success']:
            print(f"Status: {result['message']}")
            print(f"Notifications sent: {result['notification_results']}")
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")
        
        print()
        
        if i < 2:  # Don't wait after the last check
            print("Waiting 3 seconds before next check...")
            time.sleep(3)
    
    print("Demo completed! 🎉")
    print()
    print("To run the actual application:")
    print("- Single check: python main.py")
    print("- Scheduled monitoring: python run.py")
    print("- Run tests: python test_app.py")

if __name__ == "__main__":
    try:
        demo_notifications()
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Demo error: {e}")
        sys.exit(1)
