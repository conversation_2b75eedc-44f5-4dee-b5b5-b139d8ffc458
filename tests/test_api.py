#!/usr/bin/env python3
"""Test script to directly test the TikTok API call."""

import http.client
import json

def test_tiktok_api():
    """Test the TikTok API call directly."""
    print("Testing TikTok API call...")
    
    try:
        conn = http.client.HTTPSConnection("tiktok-api23.p.rapidapi.com")

        headers = {
            'x-rapidapi-key': "**************************************************",
            'x-rapidapi-host': "tiktok-api23.p.rapidapi.com"
        }

        endpoint = "/api/user/posts?secUid=MS4wLjABAAAAJZ9YAdykfgMe2R0RynUFCnUtXi3OneYGZe64dwJ-E8R-AHHCzUNN-6MGTA1mlFgd&count=35&cursor=0"
        
        print(f"Making request to: {endpoint}")
        conn.request("GET", endpoint, headers=headers)

        res = conn.getresponse()
        data = res.read()

        print(f"Status: {res.status}")
        print(f"Response length: {len(data)} bytes")
        
        if res.status == 200:
            try:
                response_data = json.loads(data.decode("utf-8"))
                print("Successfully parsed JSON response")
                
                # Print the structure
                if isinstance(response_data, dict):
                    print(f"Response keys: {list(response_data.keys())}")
                    
                    # Print first few levels of the structure
                    for key, value in response_data.items():
                        if isinstance(value, dict):
                            print(f"  {key}: dict with keys {list(value.keys())}")
                        elif isinstance(value, list):
                            print(f"  {key}: list with {len(value)} items")
                            if value and isinstance(value[0], dict):
                                print(f"    First item keys: {list(value[0].keys())}")
                        else:
                            print(f"  {key}: {type(value).__name__} = {value}")
                
                # Print the full response if it's small enough
                response_str = json.dumps(response_data, indent=2)
                if len(response_str) < 2000:
                    print("\nFull response:")
                    print(response_str)
                else:
                    print(f"\nResponse too large to print ({len(response_str)} chars)")
                    
            except json.JSONDecodeError as e:
                print(f"Failed to parse JSON: {e}")
                print("Raw response:")
                print(data.decode("utf-8")[:1000])
        else:
            print(f"API request failed with status {res.status}")
            print("Response:")
            print(data.decode("utf-8"))
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        try:
            conn.close()
        except:
            pass

if __name__ == "__main__":
    test_tiktok_api()
