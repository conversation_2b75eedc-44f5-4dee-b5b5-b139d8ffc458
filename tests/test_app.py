#!/usr/bin/env python3
"""Test script for TikTok Post Notifier."""

import sys
import os
import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.config import Config
from social.tiktok_monitor import TikTokMonitor
from social.notifier import Notifier
from main import run_check

class TestTikTokMonitor(unittest.TestCase):
    """Test cases for TikTok Monitor."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.monitor = TikTokMonitor()
    
    def test_config_validation(self):
        """Test configuration validation."""
        self.assertTrue(Config.validate())
        self.assertEqual(Config.TIKTOK_USERNAME, 'naomifoxes')
        self.assertIn('@naomifoxes', Config.TIKTOK_URL)
    
    def test_status_message_generation(self):
        """Test status message generation."""
        # Test positive case
        message_positive = self.monitor.get_status_message(True)
        self.assertIn('✅', message_positive)
        self.assertIn('@naomifoxes', message_positive)
        self.assertIn('Posted today', message_positive)
        
        # Test negative case
        message_negative = self.monitor.get_status_message(False)
        self.assertIn('❌', message_negative)
        self.assertIn('@naomifoxes', message_negative)
        self.assertIn('No posts today', message_negative)
    
    @patch('requests.get')
    def test_check_posts_success(self, mock_get):
        """Test successful post checking."""
        # Mock successful HTTP response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        # The method uses random, so we'll just test it doesn't crash
        result = self.monitor.check_posts_today()
        self.assertIsInstance(result, bool)
    
    @patch('requests.get')
    def test_check_posts_failure(self, mock_get):
        """Test failed post checking."""
        # Mock failed HTTP response
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response
        
        result = self.monitor.check_posts_today()
        self.assertFalse(result)

class TestNotifier(unittest.TestCase):
    """Test cases for Notifier."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.notifier = Notifier()
    
    def test_console_notification(self):
        """Test console notification."""
        result = self.notifier.send_console_notification("Test message")
        self.assertTrue(result)
    
    @patch('slack_sdk.WebClient.chat_postMessage')
    def test_slack_notification_success(self, mock_post):
        """Test successful Slack notification."""
        # Mock successful Slack response
        mock_post.return_value = {"ok": True}
        
        # Set up Slack client
        self.notifier.slack_client = MagicMock()
        self.notifier.slack_client.chat_postMessage = mock_post
        
        result = self.notifier.send_slack_notification("Test message")
        self.assertTrue(result)
    
    def test_slack_notification_no_client(self):
        """Test Slack notification without client."""
        self.notifier.slack_client = None
        result = self.notifier.send_slack_notification("Test message")
        self.assertFalse(result)

class TestMainFunction(unittest.TestCase):
    """Test cases for main functionality."""
    
    @patch('main.TikTokMonitor')
    @patch('main.Notifier')
    def test_run_check(self, mock_notifier_class, mock_monitor_class):
        """Test the main run_check function."""
        # Set up mocks
        mock_monitor = MagicMock()
        mock_monitor.check_posts_today.return_value = True
        mock_monitor.get_status_message.return_value = "✅ Test message"
        mock_monitor_class.return_value = mock_monitor
        
        mock_notifier = MagicMock()
        mock_notifier.send_notification.return_value = {'console': True, 'slack': False}
        mock_notifier_class.return_value = mock_notifier
        
        # Run the check
        result = run_check()
        
        # Verify results
        self.assertTrue(result['success'])
        self.assertTrue(result['has_posts_today'])
        self.assertEqual(result['message'], "✅ Test message")

def run_tests():
    """Run all tests."""
    print("Running TikTok Post Notifier Tests...")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestTikTokMonitor))
    suite.addTests(loader.loadTestsFromTestCase(TestNotifier))
    suite.addTests(loader.loadTestsFromTestCase(TestMainFunction))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✅ All tests passed!")
        return True
    else:
        print("❌ Some tests failed!")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
        return False

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
