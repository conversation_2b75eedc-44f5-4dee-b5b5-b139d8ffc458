#!/usr/bin/env python3
"""
Test script to check if we can access OnlyMonster tracking links directly
"""
import requests
from core.config import <PERSON><PERSON><PERSON>, PASSWORD, TRACKING_LINKS_URL

def test_direct_access():
    """Test if we can access the tracking links page directly"""
    session = requests.Session()
    
    # Set headers to mimic a browser
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    try:
        # Try to access the tracking links page directly
        print("Attempting to access tracking links page directly...")
        response = session.get(TRACKING_LINKS_URL)
        print(f"Status code: {response.status_code}")
        print(f"Response length: {len(response.text)}")
        
        # Check if we're redirected to login
        if "auth" in response.url or "signin" in response.url:
            print("Redirected to login page - authentication required")
            return False
        elif "tracking-links" in response.url:
            print("Successfully accessed tracking links page!")
            # Save the response for analysis
            with open("direct_access_response.html", "w", encoding="utf-8") as f:
                f.write(response.text)
            print("Response saved to direct_access_response.html")
            return True
        else:
            print(f"Unexpected redirect to: {response.url}")
            return False
            
    except Exception as e:
        print(f"Error accessing page: {e}")
        return False

if __name__ == "__main__":
    test_direct_access()
