#!/usr/bin/env python3
"""
Test script for earnings tracking functionality
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.database import TrackingDatabase
from core.analytics import TrackingAnalytics
import datetime

def test_earnings_functionality():
    """Test the earnings tracking features"""
    print("🧪 Testing OnlyMonster Earnings Tracking Functionality")
    print("="*60)
    
    # Initialize database
    db = TrackingDatabase()
    
    # Test 1: Insert sample data with earnings
    print("\n1️⃣ Testing data insertion with earnings...")
    sample_data = [
        ("test-link-1", 1000, 50, 125.50),  # name, clicks, fans, earnings
        ("test-link-2", 800, 32, 89.75),
        ("test-link-3", 1200, 48, 156.00),
        ("test-link-4", 500, 25, 0.00),     # Zero earnings platform
    ]
    
    try:
        db.insert_tracking_data(sample_data)
        print("✅ Successfully inserted sample data with earnings")
    except Exception as e:
        print(f"❌ Error inserting data: {e}")
        return
    
    # Test 2: Retrieve latest data
    print("\n2️⃣ Testing data retrieval...")
    try:
        latest_data = db.get_latest_data(10)
        print(f"✅ Retrieved {len(latest_data)} records")
        for record in latest_data[-4:]:  # Show last 4
            if len(record) >= 6:
                name, clicks, fans, earnings, revenue, timestamp = record[:6]
                print(f"   {name}: {clicks} clicks, {fans} fans, ${earnings:.2f} earnings")
            else:
                print(f"   Old format record: {record}")
    except Exception as e:
        print(f"❌ Error retrieving data: {e}")
    
    # Test 3: Update earnings
    print("\n3️⃣ Testing earnings updates...")
    try:
        db.update_earnings("test-link-1", 200.00)
        print("✅ Successfully updated earnings for test-link-1")
    except Exception as e:
        print(f"❌ Error updating earnings: {e}")
    
    # Test 4: Earnings summary
    print("\n4️⃣ Testing earnings summary...")
    try:
        summary = db.get_earnings_summary(7)
        print(f"✅ Generated earnings summary for {len(summary)} links")
        for row in summary:
            if len(row) >= 7:
                link_name, total_earnings, data_points, avg_earnings, max_earnings, first_date, last_date = row
                print(f"   {link_name}: Total ${total_earnings:.2f}, Avg ${avg_earnings:.2f}")
    except Exception as e:
        print(f"❌ Error generating summary: {e}")
    
    # Test 5: Analytics with earnings
    print("\n5️⃣ Testing analytics with earnings...")
    try:
        analytics = TrackingAnalytics()
        
        # Insert some older data for comparison
        older_data = [
            ("test-link-1", 900, 45, 100.00),  # Lower earnings
            ("test-link-2", 750, 30, 75.00),
            ("test-link-3", 1100, 44, 140.00),
            ("test-link-4", 450, 23, 0.00),    # Still zero earnings
        ]
        
        # Manually set older timestamp
        import sqlite3
        timestamp = datetime.datetime.now() - datetime.timedelta(hours=25)
        with sqlite3.connect(db.db_path) as conn:
            cursor = conn.cursor()
            cursor.executemany('''
                INSERT INTO tracking_data (tracking_link_name, clicks, fans, earnings, revenue, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', [(name, clicks, fans, earnings, 0.00, timestamp) for name, clicks, fans, earnings in older_data])
            conn.commit()
        
        print("✅ Inserted historical data for comparison")
        
        # Run analysis
        analysis = analytics.run_analysis(24)
        print("✅ Successfully ran analytics with earnings data")
        
        # Show some results
        changes = analysis['changes']
        print(f"\n📊 Earnings changes found for {len(changes['new_earnings'])} links:")
        for link, earnings_change in changes['new_earnings'].items():
            if earnings_change != 0:
                print(f"   {link}: ${earnings_change:+.2f}")
        
        print(f"\n💎 Earnings per fan analysis:")
        for link, epf_data in changes['earnings_per_fan'].items():
            if epf_data['current'] > 0:
                print(f"   {link}: ${epf_data['current']:.2f}/fan")
        
        print(f"\n⚠️  Zero earnings platforms:")
        zero_earnings = [link for link, epf_data in changes['earnings_per_fan'].items() 
                        if epf_data['current'] == 0 and link in changes['new_fans'] and changes['new_fans'][link] > 0]
        for link in zero_earnings:
            fans = changes['new_fans'].get(link, 0)
            print(f"   {link}: {fans} fans but $0.00 earnings")
        
    except Exception as e:
        print(f"❌ Error in analytics: {e}")
    
    print("\n✅ Earnings tracking functionality test completed!")
    print("\n💡 Key insights:")
    print("   1. Earnings are automatically captured from OnlyMonster platform")
    print("   2. Analytics identify highest earnings per fan platforms")
    print("   3. Zero earnings platforms are flagged for optimization")
    print("   4. AI analysis focuses on earnings performance")

def cleanup_test_data():
    """Clean up test data"""
    print("\n🧹 Cleaning up test data...")
    try:
        import sqlite3
        from core.config import DATABASE_PATH
        
        with sqlite3.connect(DATABASE_PATH) as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM tracking_data WHERE tracking_link_name LIKE 'test-link-%'")
            deleted = cursor.rowcount
            conn.commit()
        
        print(f"✅ Cleaned up {deleted} test records")
    except Exception as e:
        print(f"❌ Error cleaning up: {e}")

if __name__ == "__main__":
    test_earnings_functionality()
    
    # Ask user if they want to clean up test data
    response = input("\n🗑️  Clean up test data? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        cleanup_test_data()
    else:
        print("💾 Test data preserved for further testing")
