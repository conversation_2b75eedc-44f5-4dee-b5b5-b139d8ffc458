#!/usr/bin/env python3
"""Test script to directly test the Instagram API call."""

import http.client
import json

def test_instagram_api():
    """Test the Instagram API call directly."""
    print("Testing Instagram API call...")
    
    try:
        conn = http.client.HTTPSConnection("instagram120.p.rapidapi.com")

        # Using the exact format from your example
        payload = json.dumps({
            "username": "fox1na<PERSON>",
            "maxId": ""
        })

        headers = {
            'x-rapidapi-key': "**************************************************",
            'x-rapidapi-host': "instagram120.p.rapidapi.com",
            'Content-Type': "application/json"
        }

        endpoint = "/api/instagram/posts"
        
        print(f"Making request to: {endpoint}")
        print(f"Payload: {payload}")
        conn.request("POST", endpoint, payload, headers)

        res = conn.getresponse()
        data = res.read()

        print(f"Status: {res.status}")
        print(f"Response length: {len(data)} bytes")
        
        try:
            response_data = json.loads(data.decode("utf-8"))
            print("Successfully parsed JSON response")
            print(f"Response keys: {list(response_data.keys()) if isinstance(response_data, dict) else 'Not a dict'}")
            
            # Print response structure
            if isinstance(response_data, dict):
                for key, value in response_data.items():
                    if isinstance(value, dict):
                        print(f"  {key}: dict with keys {list(value.keys())}")
                    elif isinstance(value, list):
                        print(f"  {key}: list with {len(value)} items")
                    else:
                        print(f"  {key}: {type(value).__name__}")
            
            # Print sample of response structure
            if 'result' in response_data and 'edges' in response_data['result']:
                edges = response_data['result']['edges']
                print(f"\nFound {len(edges)} posts")
                if edges:
                    print("Sample post structure:")
                    sample_post = edges[0].get('node', {})
                    print(f"  Post keys: {list(sample_post.keys())}")

                    # Look for timestamp fields
                    timestamp_fields = ['created_at', 'timestamp', 'taken_at', 'created_time', 'date']
                    for field in timestamp_fields:
                        if field in sample_post:
                            print(f"  Found timestamp field '{field}': {sample_post[field]}")

            # Print full response if it's small enough
            response_str = str(response_data)
            if len(response_str) < 1000:
                print(f"\nFull response: {response_data}")
            else:
                print(f"\nResponse too large to print ({len(response_str)} chars)")
                
        except json.JSONDecodeError as e:
            print(f"Failed to parse JSON: {e}")
            print(f"Raw response: {data.decode('utf-8')}")
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        try:
            conn.close()
        except:
            pass

if __name__ == "__main__":
    test_instagram_api()
