#!/usr/bin/env python3
"""Test script to demonstrate Instagram API retry functionality."""

import logging
from social.instagram_checker import Instagram<PERSON>he<PERSON>

# Set up logging to see retry behavior
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_retry_functionality():
    """Test the Instagram API retry functionality."""
    print("Testing Instagram API Retry Functionality")
    print("=" * 50)
    
    # Create checker with default retry settings
    checker = InstagramChecker()
    
    print(f"Default retry settings:")
    print(f"  Max retries: {checker.max_retries}")
    print(f"  Retry delay: {checker.retry_delay} seconds")
    print(f"  Backoff multiplier: {checker.backoff_multiplier}")
    print()
    
    # Test with one account to see retry behavior
    print("Testing with @fox1naomi (may show retry attempts if API fails)...")
    print("-" * 50)
    
    has_posts, posts = checker.check_account_posts_today("fox1naomi")
    
    print("-" * 50)
    print(f"Final result: has_posts={has_posts}, posts_count={len(posts)}")
    
    if has_posts:
        print("✅ Successfully retrieved Instagram data!")
        for i, post_time in enumerate(posts[:3], 1):
            print(f"  Post {i}: {post_time.strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print("❌ Failed to retrieve Instagram data (after retries)")
    
    print("\nRetry functionality test completed!")

if __name__ == "__main__":
    test_retry_functionality()
