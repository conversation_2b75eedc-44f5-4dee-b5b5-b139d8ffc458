#!/usr/bin/env python3
"""
Test script to verify the OnlyMonster scraper fix
"""
import sys
from core.onlymonster_scraper import OnlyMonsterScraper

def test_scraper():
    """Test the scraper with a timeout"""
    print("🧪 Testing OnlyMonster Scraper Fix")
    print("=" * 50)
    
    scraper = OnlyMonsterScraper()
    try:
        print("🚀 Starting scraper test...")
        data = scraper.run_scraping()
        
        if data:
            print(f"✅ SUCCESS: Scraped {len(data)} tracking links")
            print("\n📊 Sample data:")
            for i, (name, clicks, fans, earnings) in enumerate(data[:5]):
                print(f"  {i+1}. {name}: {clicks} clicks, {fans} fans, ${earnings:.2f}")
            
            if len(data) > 5:
                print(f"  ... and {len(data) - 5} more links")
            
            return True
        else:
            print("❌ FAILED: No data collected")
            return False
            
    except Exception as e:
        print(f"❌ FAILED: {e}")
        return False

if __name__ == "__main__":
    success = test_scraper()
    sys.exit(0 if success else 1)
