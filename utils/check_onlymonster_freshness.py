#!/usr/bin/env python3
"""
Quick sanity check for OnlyMonster data freshness and 24h changes.

Usage:
  python3 utils/check_onlymonster_freshness.py
"""
import os
import sqlite3
from datetime import datetime, timedelta

from core.config import DATABASE_PATH, PRIORITY_LINKS


def fmt(dt: str) -> str:
    return dt.split(".")[0] if isinstance(dt, str) else str(dt)


def main():
    print(f"DB: {DATABASE_PATH}")
    if not os.path.exists(DATABASE_PATH):
        print("❌ Database file not found")
        return 1

    with sqlite3.connect(DATABASE_PATH) as conn:
        cur = conn.cursor()
        cur.execute("SELECT MAX(timestamp) FROM tracking_data")
        latest = cur.fetchone()[0]
        if not latest:
            print("❌ No tracking_data present")
            return 1

        latest_dt = datetime.fromisoformat(latest)
        age_h = round((datetime.now() - latest_dt).total_seconds() / 3600, 2)
        print(f"Latest record: {fmt(latest)} ({age_h} hours ago)")

        # Compute global 24h changes by link
        cur.execute(
            '''
            SELECT t1.tracking_link_name, t1.clicks, t1.fans, t1.earnings, t1.timestamp
            FROM tracking_data t1
            INNER JOIN (
                SELECT tracking_link_name, MAX(timestamp) AS max_ts
                FROM tracking_data
                GROUP BY tracking_link_name
            ) t2 ON t1.tracking_link_name = t2.tracking_link_name AND t1.timestamp = t2.max_ts
            '''
        )
        current = cur.fetchall()

        total_clicks = 0
        total_fans = 0
        total_earnings = 0.0
        total_clicks_change = 0
        total_fans_change = 0
        total_earnings_change = 0.0

        for name, clicks, fans, earnings, ts in current:
            total_clicks += clicks
            total_fans += fans
            total_earnings += float(earnings or 0)
            probe = (datetime.fromisoformat(ts) - timedelta(hours=24)).strftime('%Y-%m-%d %H:%M:%S')
            cur.execute(
                '''SELECT clicks, fans, earnings FROM tracking_data
                   WHERE tracking_link_name=? AND timestamp <= ?
                   ORDER BY timestamp DESC LIMIT 1''',
                (name, probe)
            )
            prev = cur.fetchone()
            if prev:
                pc, pf, pe = prev
                total_clicks_change += max(0, clicks - pc)
                total_fans_change += max(0, fans - pf)
                total_earnings_change += max(0.0, float(earnings or 0) - float(pe or 0))

        print("\nAggregate (current totals):")
        print(f"  Clicks: {total_clicks:,}")
        print(f"  Fans: {total_fans:,}")
        print(f"  Earnings: ${total_earnings:,.2f}")
        print("\nEstimated 24h changes (sum over links):")
        print(f"  New Clicks: {total_clicks_change:,}")
        print(f"  New Fans: {total_fans_change:,}")
        print(f"  Earnings Change: ${total_earnings_change:,.2f}")

        print("\nPriority links sample (latest timestamps):")
        for pl in PRIORITY_LINKS:
            cur.execute(
                'SELECT MAX(timestamp) FROM tracking_data WHERE tracking_link_name=?',
                (pl,)
            )
            lts = cur.fetchone()[0]
            print(f"  {pl}: {fmt(lts) if lts else '—'}")

    return 0


if __name__ == '__main__':
    raise SystemExit(main())

