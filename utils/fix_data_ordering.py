#!/usr/bin/env python3
"""
Utility script to fix data insertion order issues in the OnlyMonster database.
Run this if historical data was inserted after current data, causing report issues.
"""
import sqlite3
from core.config import DATABASE_PATH

def fix_data_ordering():
    """Fix data ordering by re-inserting all data in chronological order"""
    print("🔧 Fixing OnlyMonster database data ordering...")
    
    with sqlite3.connect(DATABASE_PATH) as conn:
        cursor = conn.cursor()
        
        # Check current state
        cursor.execute('SELECT MAX(timestamp) FROM tracking_data')
        latest_timestamp = cursor.fetchone()[0]
        
        cursor.execute('SELECT timestamp FROM tracking_data ORDER BY id DESC LIMIT 1')
        latest_entry = cursor.fetchone()[0]
        
        if latest_timestamp == latest_entry:
            print("✅ Data ordering is already correct!")
            return True
        
        print(f"❌ Data ordering issue detected:")
        print(f"   Latest timestamp: {latest_timestamp}")
        print(f"   Latest entry: {latest_entry}")
        
        # Get all data sorted by timestamp (chronological order)
        cursor.execute('''
            SELECT tracking_link_name, clicks, fans, earnings, timestamp
            FROM tracking_data
            ORDER BY timestamp ASC
        ''')
        
        all_data = cursor.fetchall()
        print(f"📊 Extracted {len(all_data)} records")
        
        # Clear the table
        cursor.execute('DELETE FROM tracking_data')
        print("🗑️  Cleared existing data")
        
        # Re-insert in chronological order
        cursor.executemany('''
            INSERT INTO tracking_data (tracking_link_name, clicks, fans, earnings, timestamp)
            VALUES (?, ?, ?, ?, ?)
        ''', all_data)
        
        conn.commit()
        print(f"✅ Re-inserted {len(all_data)} records in chronological order")
        
        # Verify the fix
        cursor.execute('SELECT MAX(timestamp) FROM tracking_data')
        new_latest_timestamp = cursor.fetchone()[0]
        
        cursor.execute('SELECT timestamp FROM tracking_data ORDER BY id DESC LIMIT 1')
        new_latest_entry = cursor.fetchone()[0]
        
        if new_latest_timestamp == new_latest_entry:
            print("✅ Data ordering successfully fixed!")
            
            # Show summary
            cursor.execute('''
                SELECT DATE(timestamp) as date, COUNT(*) as count, MIN(id) as min_id, MAX(id) as max_id
                FROM tracking_data
                GROUP BY DATE(timestamp)
                ORDER BY date ASC
            ''')
            
            dates = cursor.fetchall()
            print(f'\n📅 Fixed data order:')
            print('Date       | Count | ID Range')
            print('-' * 35)
            for date, count, min_id, max_id in dates:
                print(f'{date} | {count:>5} | {min_id}-{max_id}')
            
            return True
        else:
            print("❌ Failed to fix data ordering!")
            return False

def main():
    """Main function"""
    success = fix_data_ordering()
    
    if success:
        print("\n🎯 Data ordering is now correct!")
        print("📊 Your daily reports will now show accurate comparisons.")
        print("🚀 You can run the daily report to verify the fix.")
    else:
        print("\n❌ Failed to fix data ordering. Please check the database manually.")

if __name__ == "__main__":
    main()
