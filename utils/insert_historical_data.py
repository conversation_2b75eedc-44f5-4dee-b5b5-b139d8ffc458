#!/usr/bin/env python3
"""
Insert historical OnlyMonster data from the provided table
"""

from core.database import TrackingDatabase
from datetime import datetime

def insert_historical_data():
    """Insert the historical data from the table"""
    
    print("📊 Inserting Historical OnlyMonster Data")
    print("=" * 50)
    
    # Historical data structure: fans data (above grey bar)
    fans_data = {
        'reddit-babycheeksx': {
            '3-15': 112, '3-22': 115, '3-23': 116, '3-24': 116, '3-28': 117,
            '4-12': 120, '5-17': 125, '5-29': 127, '6-4': 130
        },
        'reels-naominoface': {
            '3-15': 23, '3-22': 25, '3-23': 26, '3-24': 27, '3-28': 32,
            '4-12': 50, '5-17': 98, '5-29': 110, '6-4': 122
        },
        'reels-lilfoxnaomi-aug-22': {
            '3-15': 136, '3-22': 138, '3-23': 139, '3-24': 139, '3-28': 141,
            '4-12': 147, '5-17': 156, '5-29': 156, '6-4': 158
        },
        'chive-nyla-aug-8': {
            '3-15': 15, '3-22': 15, '3-23': 15, '3-24': 15, '3-28': 15,
            '4-12': 15, '5-17': 16, '5-29': 16, '6-4': 16
        },
        'tiktok-aug-1-24': {
            '3-15': 113, '3-22': 117, '3-23': 117, '3-24': 117, '3-28': 119,
            '4-12': 121, '5-17': 132, '5-29': 134, '6-4': 134
        },
        'chive-aug-1-24': {
            '3-15': 373, '3-22': 383, '3-23': 386, '3-24': 386, '3-28': 394,
            '4-12': 411, '5-17': 443, '5-29': 455, '6-4': 466
        },
        'Reels2024': {
            '3-15': 857, '3-22': 879, '3-23': 888, '3-24': 891, '3-28': 921,
            '4-12': 1041, '5-17': 1207, '5-29': 1231, '6-4': 1246
        }
    }
    
    # Clicks data (below grey bar)
    clicks_data = {
        'reddit-babycheeksx': {
            '3-15': 3580, '3-22': 3640, '3-23': 3652, '3-24': 3653, '3-28': 3688,
            '4-12': 3891, '5-17': 4209, '5-29': 4343, '6-4': 4442
        },
        'reels-naominoface': {
            '3-15': 2200, '3-22': 2431, '3-23': 2480, '3-24': 2510, '3-28': 2717,
            '4-12': 3716, '5-17': 8386, '5-29': 8914, '6-4': 9983
        },
        'reels-lilfoxnaomi-aug-22': {
            '3-15': 17970, '3-22': 18165, '3-23': 18233, '3-24': 18274, '3-28': 18434,
            '4-12': 19040, '5-17': 20119, '5-29': 20390, '6-4': 20540
        },
        'chive-nyla-aug-8': {
            '3-15': 3748, '3-22': 3748, '3-23': 3748, '3-24': 3748, '3-28': 3750,
            '4-12': 3752, '5-17': 3760, '5-29': 3762, '6-4': 3764
        },
        'tiktok-aug-1-24': {
            '3-15': 7414, '3-22': 7513, '3-23': 7533, '3-24': 7568, '3-28': 7610,
            '4-12': 7777, '5-17': 8035, '5-29': 8074, '6-4': 8105
        },
        'chive-aug-1-24': {
            '3-15': 38278, '3-22': 39279, '3-23': 39612, '3-24': 39689, '3-28': 40118,
            '4-12': 41825, '5-17': 46354, '5-29': 47771, '6-4': 48720
        },
        'Reels2024': {
            '3-15': 80073, '3-22': 81896, '3-23': 82288, '3-24': 82546, '3-28': 84207,
            '4-12': 90765, '5-17': 99059, '5-29': 100512, '6-4': 101828
        }
    }
    
    # Date mapping to proper timestamps (using 2025 as current year)
    date_mapping = {
        '3-15': '2025-03-15 12:00:00',
        '3-22': '2025-03-22 12:00:00', 
        '3-23': '2025-03-23 12:00:00',
        '3-24': '2025-03-24 12:00:00',
        '3-28': '2025-03-28 12:00:00',
        '4-12': '2025-04-12 12:00:00',
        '5-17': '2025-05-17 12:00:00',
        '5-29': '2025-05-29 12:00:00',
        '6-4': '2025-06-04 12:00:00'
    }
    
    # Initialize database
    db = TrackingDatabase()
    
    # Prepare data for insertion
    all_data = []
    
    for date_key, timestamp in date_mapping.items():
        print(f"\nProcessing date: {date_key} ({timestamp})")
        
        for link_name in fans_data.keys():
            fans = fans_data[link_name][date_key]
            clicks = clicks_data[link_name][date_key]
            earnings = None  # Set to None for null revenue
            
            # Add to batch data (name, clicks, fans, earnings)
            all_data.append((link_name, clicks, fans, earnings))
            print(f"  {link_name}: {clicks} clicks, {fans} fans, earnings=null")
    
    print(f"\n📥 Inserting {len(all_data)} historical records...")
    
    # Insert all data at once
    try:
        # We need to insert each date separately to maintain proper timestamps
        for date_key, timestamp in date_mapping.items():
            date_data = []
            for link_name in fans_data.keys():
                fans = fans_data[link_name][date_key]
                clicks = clicks_data[link_name][date_key]
                earnings = 0.00  # Use 0.00 instead of None for database compatibility
                date_data.append((link_name, clicks, fans, earnings))
            
            # Insert with specific timestamp
            db.insert_tracking_data_with_timestamp(date_data, timestamp)
            print(f"✅ Inserted {len(date_data)} records for {date_key}")
        
        print(f"\n🎉 Successfully inserted all historical data!")
        
        # Show summary
        print(f"\n📊 SUMMARY:")
        print(f"• Total records inserted: {len(all_data)}")
        print(f"• Date range: 3/15/25 to 6/4/25")
        print(f"• Links covered: {len(fans_data)} tracking links")
        print(f"• Revenue values: Set to $0.00 (null equivalent)")
        
    except Exception as e:
        print(f"❌ Error inserting data: {e}")

if __name__ == "__main__":
    insert_historical_data()
