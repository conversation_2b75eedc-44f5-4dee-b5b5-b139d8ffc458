#!/usr/bin/env python3
"""
Task Manager for OnlyMonster Dashboard
Handles task completion workflow with automatic webapp refresh
"""
import re
import subprocess
import time
from datetime import datetime
from pathlib import Path

class TaskManager:
    def __init__(self, tasks_file="/root/onlymonster-automations/TASKS.md"):
        self.tasks_file = Path(tasks_file)
        
    def read_tasks(self):
        """Read and parse tasks from the TASKS.md file"""
        if not self.tasks_file.exists():
            return {"pending": [], "completed": []}
            
        content = self.tasks_file.read_text()
        
        # Find pending tasks section (everything between "## Pending Tasks" and "## Completed Tasks")
        pending_section = re.search(r'## Pending Tasks\s*\n(.*?)(?=^## Completed Tasks|\Z)', content, re.MULTILINE | re.DOTALL)
        pending_tasks = []
        
        if pending_section:
            # Extract tasks marked with - [ ] (each task on a single line)
            lines = pending_section.group(1).split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('- [ ]'):
                    task = line[6:].strip()  # Remove '- [ ] ' prefix
                    # Clean up task text (remove markdown formatting)
                    clean_task = re.sub(r'\*\*(.*?)\*\*:?', r'\1', task)  # Remove bold formatting
                    if clean_task:  # Only add non-empty tasks
                        pending_tasks.append(clean_task)
        
        # Find completed tasks section
        completed_section = re.search(r'## Completed Tasks ✅\s*\n(.*?)(?=^##|\Z)', content, re.MULTILINE | re.DOTALL)
        completed_tasks = []
        
        if completed_section:
            lines = completed_section.group(1).split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('- [x]'):
                    task = line[6:].strip()  # Remove '- [x] ' prefix
                    # Clean up task text (remove markdown formatting and timestamps)
                    clean_task = re.sub(r'\*\*(.*?)\*\*:?', r'\1', task)  # Remove bold formatting
                    clean_task = re.sub(r'✅.*$', '', clean_task).strip()  # Remove completion timestamps
                    if clean_task:  # Only add non-empty tasks
                        completed_tasks.append(clean_task)
        
        return {"pending": pending_tasks, "completed": completed_tasks}
    
    def mark_task_completed(self, task_text):
        """Mark a task as completed and move it to completed section"""
        content = self.tasks_file.read_text()
        
        # Find the exact task line and replace [ ] with [x]
        task_pattern = re.escape(task_text)
        pattern = rf'(- \[ \] .*?{task_pattern}.*?)(?=\n- \[|\n\n|\n##|\Z)'
        
        def replace_task(match):
            task_line = match.group(1)
            return task_line.replace('- [ ]', '- [x]')
        
        updated_content = re.sub(pattern, replace_task, content, flags=re.DOTALL)
        
        # Add timestamp to completed task
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
        updated_content = updated_content.replace(
            '- [x]', f'- [x] ✅ *Completed {timestamp}*', 1
        )
        
        self.tasks_file.write_text(updated_content)
        
    def refresh_webapp(self):
        """Refresh the webapp by rebuilding and restarting Docker container"""
        print("🔄 Refreshing webapp...")
        
        try:
            # Rebuild and restart the container
            result = subprocess.run([
                'docker', 'compose', 'build', '--no-cache', 'cheeksdash'
            ], cwd='/root/onlymonster-automations/web_dashboard', 
               capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                print(f"❌ Build failed: {result.stderr}")
                return False
                
            result = subprocess.run([
                'docker', 'compose', 'up', '-d', 'cheeksdash'
            ], cwd='/root/onlymonster-automations/web_dashboard',
               capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                print(f"❌ Container restart failed: {result.stderr}")
                return False
            
            # Wait for container to be ready
            print("⏳ Waiting for webapp to be ready...")
            time.sleep(5)
            
            # Test that the webapp is responding
            result = subprocess.run([
                'curl', '-s', '-o', '/dev/null', '-w', '%{http_code}', 
                'https://cheeksdash.com/'
            ], capture_output=True, text=True, timeout=30)
            
            if result.stdout == '200':
                print("✅ Webapp refreshed successfully - cheeksdash.com is live!")
                return True
            else:
                print(f"⚠️  Webapp responded with status {result.stdout}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Refresh timed out")
            return False
        except Exception as e:
            print(f"❌ Refresh failed: {e}")
            return False

def print_tasks():
    """Utility function to display current tasks"""
    manager = TaskManager()
    tasks = manager.read_tasks()
    
    print("\n📋 CURRENT TASKS")
    print("=" * 50)
    
    if tasks["pending"]:
        print("\n⏳ PENDING TASKS:")
        for i, task in enumerate(tasks["pending"], 1):
            print(f"  {i}. {task}")
    else:
        print("\n✅ No pending tasks!")
    
    if tasks["completed"]:
        print(f"\n✅ COMPLETED ({len(tasks['completed'])}):")
        for task in tasks["completed"][-3:]:  # Show last 3 completed
            print(f"  • {task}")
        if len(tasks["completed"]) > 3:
            print(f"  ... and {len(tasks['completed']) - 3} more")

if __name__ == "__main__":
    print_tasks()