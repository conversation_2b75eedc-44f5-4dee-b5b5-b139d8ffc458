# Multi-Site Hosting Guide for cheeksdash.com

## 🌐 Your Current Setup
- **Server IP**: `***********`
- **Existing Setup**: Coolify with Traefik reverse proxy
- **New Site**: cheeksdash.com (OnlyMonster Analytics Dashboard)

## ✅ Why This Works Perfectly

Your existing Coolify/Traefik setup is **ideal** for hosting multiple websites:

1. **Traefik** automatically handles SSL certificates via Let's Encrypt
2. **Domain-based routing** allows multiple sites on the same server
3. **No port conflicts** - Traefik manages everything on ports 80/443
4. **Automatic HTTPS** - SSL certificates are provisioned automatically

## 🚀 Quick Setup (Recommended)

Run the Docker setup script:
```bash
cd /root/onlymonster-automations/web_dashboard
./setup-docker.sh
```

## 🌐 DNS Configuration

Configure these DNS records with your domain registrar:

### DNS Records for cheeksdash.com
```
Type: A
Name: @
Value: ***********
TTL: 300

Type: A
Name: www  
Value: ***********
TTL: 300
```

### How It Works with Multiple Sites
- Your existing site continues working on its domain
- cheeksdash.com will be routed to the new dashboard
- Traefik handles SSL certificates for both domains automatically
- No conflicts or downtime for existing sites

## 🔧 Manual Docker Setup

If you prefer manual setup:

1. **Build the Docker image**:
   ```bash
   cd /root/onlymonster-automations/web_dashboard
   docker build -t cheeksdash:latest .
   ```

2. **Start the container**:
   ```bash
   docker-compose up -d
   ```

3. **Verify it's running**:
   ```bash
   docker ps | grep cheeksdash
   ```

## 📊 Container Management

### View logs
```bash
docker logs cheeksdash -f
```

### Restart container
```bash
docker-compose restart
```

### Stop container
```bash
docker-compose down
```

### Rebuild after changes
```bash
docker-compose up --build -d
```

## 🔒 SSL & Security

### Automatic SSL
- Traefik automatically provisions SSL certificates from Let's Encrypt
- Certificates renew automatically
- HTTP automatically redirects to HTTPS

### Security Features
- Container runs as non-root user
- Health checks ensure reliability
- Rate limiting via Traefik
- Security headers automatically added

## 🌍 DNS Propagation Timeline

1. **Immediate (0-1 hours)**: Some users may see the new site
2. **4-8 hours**: Most users will see the new site  
3. **24-48 hours**: All users worldwide will see the new site

Check propagation: https://www.whatsmydns.net/#A/cheeksdash.com

## 🔍 Troubleshooting

### Container won't start
```bash
docker logs cheeksdash
```

### Domain not resolving
- Check DNS propagation with `dig cheeksdash.com`
- Verify DNS records point to `***********`

### SSL issues
- Traefik logs: `docker logs coolify-proxy`
- Allow 5-10 minutes for certificate provisioning

### Dashboard not loading
```bash
# Test local connection
curl -I http://localhost:5000

# Check if container is healthy
docker ps --filter name=cheeksdash
```

## 📱 Testing

Once DNS propagates, test these URLs:
- https://cheeksdash.com (should load dashboard)
- http://cheeksdash.com (should redirect to HTTPS)
- https://www.cheeksdash.com (should redirect to non-www)

## 🔄 Updates

To update the dashboard:
```bash
cd /root/onlymonster-automations/web_dashboard
docker-compose down
docker build -t cheeksdash:latest .
docker-compose up -d
```

## 📊 Dashboard Features

Once live, your dashboard will show:
- **OnlyMonster Analytics**: Real-time fan growth, earnings, link performance
- **Social Media Monitoring**: TikTok, Instagram, Reddit with removed post tracking
- **OnlyFans Deal Detection**: Automatic deal monitoring
- **Auto-refresh**: Updates every 5 minutes

## 🎯 Benefits of This Setup

1. **No Downtime**: Existing sites continue running
2. **Professional SSL**: Automatic certificates for both domains
3. **High Performance**: Traefik load balancing and caching
4. **Easy Management**: All sites managed through Coolify interface
5. **Scalable**: Can easily add more domains/services

Your dashboard will be live at **https://cheeksdash.com** once DNS propagates!