# Cheeks Dashboard - OnlyMonster Analytics Web Interface

A modern web dashboard for viewing OnlyMonster analytics and social media activity in real-time.

## 🌟 Features

- **OnlyMonster Analytics**: Fan growth, click tracking, earnings, conversion rates
- **Social Media Monitoring**: TikTok, Instagram, Reddit post tracking with removed post analysis  
- **OnlyFans Deal Detection**: Automatic deal monitoring and display
- **Real-time Updates**: Auto-refreshing data every 5 minutes
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Secure Hosting**: SSL encryption, rate limiting, security headers

## 📊 Dashboard Sections

### Overview Cards
- **OnlyMonster Metrics**: New fans, clicks, earnings, active links
- **Social Media Summary**: Total posts, active accounts, Reddit removals, OnlyFans deals

### OnlyMonster Analytics
- **Link Performance Table**: Detailed metrics for all tracking links
- **Earnings Breakdown**: Daily changes and top earning sources

### Social Media Analytics
- **TikTok**: Account activity with post times
- **Instagram**: Account activity with error handling
- **Reddit**: Post tracking with removal reasons (subreddit_not_allowed, submitted_incorrectly, etc.)

## 🚀 Setup Instructions

### Prerequisites
- Ubuntu/Debian server with root access
- Python 3.8+ with existing OnlyMonster automation setup
- Domain name (cheeksdash.com) pointed to your server

### Quick Setup
1. Run the setup script:
   ```bash
   cd /root/onlymonster-automations/web_dashboard
   chmod +x setup.sh
   ./setup.sh
   ```

### Manual Setup
1. **Install Dependencies**:
   ```bash
   source /root/onlymonster-automations/venv/bin/activate
   pip install -r requirements.txt
   ```

2. **Configure Nginx**:
   ```bash
   sudo cp nginx.conf /etc/nginx/sites-available/cheeksdash.com
   sudo ln -sf /etc/nginx/sites-available/cheeksdash.com /etc/nginx/sites-enabled/
   sudo nginx -t
   ```

3. **Install SSL Certificate**:
   ```bash
   sudo certbot --nginx -d cheeksdash.com -d www.cheeksdash.com
   ```

4. **Setup SystemD Service**:
   ```bash
   sudo cp systemd.service /etc/systemd/system/cheeksdash.service
   sudo systemctl daemon-reload
   sudo systemctl enable cheeksdash.service
   sudo systemctl start cheeksdash.service
   ```

5. **Start Services**:
   ```bash
   sudo systemctl restart nginx
   sudo systemctl start cheeksdash
   ```

## 🌐 DNS Configuration for cheeksdash.com

Configure these DNS records with your domain registrar:

### A Records
```
Type: A
Name: @
Value: [YOUR_SERVER_IP]
TTL: 300

Type: A  
Name: www
Value: [YOUR_SERVER_IP]
TTL: 300
```

### CNAME (Alternative for www)
```
Type: CNAME
Name: www
Value: cheeksdash.com
TTL: 300
```

### Common DNS Providers

#### Cloudflare
1. Login to Cloudflare Dashboard
2. Select your domain
3. Go to DNS → Records
4. Add A record: `@` → `[YOUR_SERVER_IP]`
5. Add A record: `www` → `[YOUR_SERVER_IP]`
6. Set Proxy Status to "Proxied" for DDoS protection (optional)

#### Namecheap
1. Login to Namecheap Account
2. Go to Domain List → Manage
3. Advanced DNS → Add New Record
4. A Record: `@` → `[YOUR_SERVER_IP]`
5. A Record: `www` → `[YOUR_SERVER_IP]`

#### GoDaddy
1. Login to GoDaddy Account
2. My Products → DNS
3. Records → Add
4. Type: A, Host: @, Points to: `[YOUR_SERVER_IP]`
5. Type: A, Host: www, Points to: `[YOUR_SERVER_IP]`

#### Google Domains
1. Login to Google Domains
2. Manage → DNS
3. Custom Records → Create New Record
4. A record: @ → `[YOUR_SERVER_IP]`
5. A record: www → `[YOUR_SERVER_IP]`

### DNS Propagation
- DNS changes can take 24-48 hours to fully propagate
- Use `dig cheeksdash.com` or online tools to check propagation
- Test with: `curl -I http://cheeksdash.com`

## 🔒 Security Features

- **SSL/TLS Encryption**: Automatic HTTPS redirection
- **Security Headers**: XSS protection, content type options, frame options
- **Rate Limiting**: API endpoint protection (10 requests/second)
- **Access Logging**: Full request/response logging
- **Firewall Ready**: Nginx configuration supports fail2ban integration

## 📱 API Endpoints

- `GET /` - Main dashboard
- `GET /api/onlymonster/overview` - OnlyMonster summary data
- `GET /api/onlymonster/metrics` - Detailed link performance
- `GET /api/onlymonster/earnings` - Earnings breakdown
- `GET /api/social/overview` - Social media summary  
- `GET /api/social/details` - Detailed social account data

## 🔧 Maintenance

### View Logs
```bash
# Application logs
sudo journalctl -u cheeksdash.service -f

# Nginx logs
sudo tail -f /var/log/nginx/cheeksdash_access.log
sudo tail -f /var/log/nginx/cheeksdash_error.log
```

### Restart Services
```bash
sudo systemctl restart cheeksdash.service
sudo systemctl restart nginx
```

### Update Dashboard
```bash
cd /root/onlymonster-automations
git pull  # if using git
sudo systemctl restart cheeksdash.service
```

## 🎨 Customization

### Colors & Styling
Edit `/static/css/dashboard.css` to customize:
- Color scheme (CSS variables in `:root`)
- Card layouts and spacing
- Typography and fonts
- Responsive breakpoints

### Data Sources
Modify `app.py` to:
- Add new API endpoints
- Integrate additional data sources
- Customize metrics calculations
- Add new dashboard sections

## ⚡ Performance

- **Caching**: Static assets cached for 1 year
- **Compression**: Gzip enabled for text content  
- **Auto-refresh**: 5-minute intervals to reduce server load
- **Efficient Queries**: Optimized database queries
- **Rate Limiting**: Prevents API abuse

## 📞 Support

Dashboard URL: https://cheeksdash.com
Repository: OnlyMonster Automations
Created: 2025-08-23