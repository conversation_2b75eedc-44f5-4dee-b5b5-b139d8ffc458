#!/usr/bin/env python3
"""
Cheeks Dashboard - OnlyMonster Analytics Web Interface
Enhanced with real data integration
"""
import sys
import os
import sqlite3
import json
from datetime import datetime, timedelta, timezone
from flask import Flask, render_template, jsonify
import traceback

# Add parent directory to Python path for imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)
sys.path.insert(0, '/app/data')

# Import database for historical social media data
try:
    from core.database import TrackingDatabase
    database_available = True
except ImportError:
    database_available = False
    print("Database module not available for historical data")

app = Flask(__name__)

# Global configuration - adjust paths for container and local environments
# Use core.config as the single source of truth for the default DB path.
try:
    from core.config import DATABASE_PATH as CORE_DATABASE_PATH
except Exception:
    CORE_DATABASE_PATH = os.path.join(parent_dir, "onlymonster_data.db")

# Allow override via env var (e.g., Docker sets /app/data/onlymonster_data.db)
DATABASE_PATH = os.getenv("DATABASE_PATH", CORE_DATABASE_PATH)
BACKUP_DB_PATH = CORE_DATABASE_PATH

PRIORITY_LINKS = [
    "reddit-babycheeksx-combined",
    "reels-naominoface",
    "reels-lilfoxnaomi-aug-22",
    "naominixo-threads",
    "chive-nyla-aug-8",
    "tiktok-aug-1-24",
    "chive-aug-1-24",
    "Reels2024",
    "X - naomifoxxxx",
    "X - nylabaexo",
    "instagram-naomifunxo"
]

def get_database_path():
    """Get the correct database path, checking both mounted and local paths"""
    if os.path.exists(DATABASE_PATH):
        return DATABASE_PATH
    elif os.path.exists(BACKUP_DB_PATH):
        return BACKUP_DB_PATH
    else:
        return DATABASE_PATH  # Use mounted path as fallback

# Lightweight cache for OnlyFans deal status to avoid heavy Selenium calls per request
DEAL_CACHE_PATH = os.getenv("DEAL_CACHE_PATH", "/tmp/onlyfans_deal_cache.json")

def _read_deal_cache() -> dict:
    try:
        if os.path.exists(DEAL_CACHE_PATH):
            with open(DEAL_CACHE_PATH, 'r') as f:
                return json.load(f)
    except Exception:
        pass
    return {}

def _write_deal_cache(cache: dict):
    try:
        os.makedirs(os.path.dirname(DEAL_CACHE_PATH), exist_ok=True)
        with open(DEAL_CACHE_PATH, 'w') as f:
            json.dump(cache, f)
    except Exception:
        pass

def get_onlyfans_deal_info(username: str, ttl_seconds: int = 6 * 3600) -> dict:
    """Get OnlyFans deal info with DB-first cache, then file cache, then live fetch.
    ttl_seconds applies to both DB record freshness and file cache.
    """
    try:
        now_ts = int(datetime.now().timestamp())
        # DB first (if available)
        try:
            from core.database import TrackingDatabase
            db = TrackingDatabase()
            cached = db.get_latest_onlyfans_deal(username)
            if cached and cached.get('checked_at'):
                try:
                    ts = int(datetime.fromisoformat(str(cached['checked_at'])).timestamp())
                except Exception:
                    ts = now_ts
                if now_ts - ts <= ttl_seconds:
                    return {
                        'has_deal': cached.get('has_deal', False),
                        'deal_text': cached.get('deal_text', ''),
                        'deal_percentage': cached.get('deal_percentage', 0)
                    }
        except Exception:
            pass

        # File cache fallback
        cache = _read_deal_cache()
        entry = cache.get(username)
        if entry and isinstance(entry, dict):
            ts = entry.get('cached_at') or entry.get('timestamp') or 0
            if isinstance(ts, str):
                try:
                    ts = int(datetime.fromisoformat(ts).timestamp())
                except Exception:
                    ts = 0
            if now_ts - int(ts) <= ttl_seconds:
                return entry.get('deal_info', entry)
    except Exception:
        pass

    # Cache miss or stale; fetch live with Selenium
    try:
        from social.onlyfans_checker import OnlyFansChecker
        onlyfans_checker = OnlyFansChecker()
        deal_info = onlyfans_checker.check_deal_status(username)
        # Store in DB best-effort
        try:
            from core.database import TrackingDatabase
            TrackingDatabase().upsert_onlyfans_deal(username, deal_info)
        except Exception:
            pass
    except Exception as e:
        print(f"OnlyFans checker error: {e}")
        deal_info = {'has_deal': False, 'deal_text': '', 'deal_percentage': 0}

    # Store back to cache
    try:
        cache = _read_deal_cache()
        cache[username] = {
            'deal_info': deal_info,
            'cached_at': now_ts
        }
        _write_deal_cache(cache)
    except Exception:
        pass

    return deal_info

def get_recent_data(hours=24):
    """Get recent tracking data for the dashboard"""
    db_path = get_database_path()
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()

            # Get data from the last 24 hours
            since_time = datetime.now() - timedelta(hours=hours)

            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, earnings, timestamp
                FROM tracking_data
                WHERE timestamp >= ?
                ORDER BY timestamp DESC
            ''', (since_time.strftime('%Y-%m-%d %H:%M:%S'),))

            results = cursor.fetchall()

            # Organize by link name
            data_by_link = {}
            for link_name, clicks, fans, earnings, timestamp in results:
                if link_name not in data_by_link:
                    data_by_link[link_name] = []
                data_by_link[link_name].append({
                    'clicks': clicks,
                    'fans': fans,
                    'earnings': earnings,
                    'timestamp': timestamp
                })

            return data_by_link
    except Exception as e:
        print(f"Database error: {e}")
        return {}

def get_latest_metrics():
    """Get the latest metrics for each tracking link"""
    db_path = get_database_path()
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()

            # Get the most recent entry for each link
            cursor.execute('''
                SELECT t1.tracking_link_name, t1.clicks, t1.fans, t1.earnings, t1.timestamp
                FROM tracking_data t1
                INNER JOIN (
                    SELECT tracking_link_name, MAX(timestamp) as max_timestamp
                    FROM tracking_data
                    GROUP BY tracking_link_name
                ) t2 ON t1.tracking_link_name = t2.tracking_link_name
                AND t1.timestamp = t2.max_timestamp
                ORDER BY t1.earnings DESC
            ''')

            return cursor.fetchall()
    except Exception as e:
        print(f"Database error: {e}")
        return []

def calculate_daily_changes():
    """Calculate 24-hour changes with robust handling:
    - If a baseline within the last 24h exists: use latest - earliest_in_window
    - Else use the most recent previous record and normalize by elapsed days
    - Handles multiple scrapes per day and long gaps between scrapes
    """
    db_path = get_database_path()
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()

            # Get current values (latest per link)
            current_data = {}
            cursor.execute('''
                SELECT t1.tracking_link_name, t1.clicks, t1.fans, t1.earnings, t1.timestamp
                FROM tracking_data t1
                INNER JOIN (
                    SELECT tracking_link_name, MAX(timestamp) as max_timestamp
                    FROM tracking_data
                    GROUP BY tracking_link_name
                ) t2 ON t1.tracking_link_name = t2.tracking_link_name
                AND t1.timestamp = t2.max_timestamp
            ''')
            for link, clicks, fans, earnings, ts in cursor.fetchall():
                current_data[link] = {
                    'clicks': int(clicks or 0),
                    'fans': int(fans or 0),
                    'earnings': float(earnings or 0),
                    'timestamp': ts
                }

            changes = {}
            for link, current in current_data.items():
                try:
                    latest_dt = datetime.fromisoformat(current['timestamp'])
                except Exception:
                    # Fallback parsing if timestamp might include Z
                    latest_dt = datetime.fromisoformat(current['timestamp'].replace('Z', '+00:00'))

                start_window = (latest_dt - timedelta(hours=24)).strftime('%Y-%m-%d %H:%M:%S')
                latest_ts_str = latest_dt.strftime('%Y-%m-%d %H:%M:%S')

                # 1) Try to find the earliest record within the last 24h window
                cursor.execute('''
                    SELECT clicks, fans, earnings, timestamp
                    FROM tracking_data
                    WHERE tracking_link_name = ?
                      AND timestamp >= ? AND timestamp < ?
                    ORDER BY timestamp ASC
                    LIMIT 1
                ''', (link, start_window, latest_ts_str))
                in_window = cursor.fetchone()

                if in_window:
                    b_clicks, b_fans, b_earnings, b_timestamp = in_window
                    click_delta = max(0, current['clicks'] - int(b_clicks or 0))
                    fan_delta = max(0, current['fans'] - int(b_fans or 0))

                    # Calculate the actual time difference and normalize earnings per day
                    try:
                        baseline_dt = datetime.fromisoformat(b_timestamp)
                    except Exception:
                        baseline_dt = datetime.fromisoformat(b_timestamp.replace('Z', '+00:00'))

                    days = max((latest_dt - baseline_dt).total_seconds() / 86400.0, 1.0)
                    total_earn_change = current['earnings'] - float(b_earnings or 0)
                    earn_delta = round(total_earn_change / days, 2)
                else:
                    # 2) No baseline within window. Use previous record before window and normalize per day
                    cursor.execute('''
                        SELECT clicks, fans, earnings, timestamp
                        FROM tracking_data
                        WHERE tracking_link_name = ? AND timestamp < ?
                        ORDER BY timestamp DESC
                        LIMIT 1
                    ''', (link, start_window))
                    prev = cursor.fetchone()
                    if prev:
                        p_clicks, p_fans, p_earnings, p_ts = prev
                        try:
                            prev_dt = datetime.fromisoformat(p_ts)
                        except Exception:
                            prev_dt = datetime.fromisoformat(p_ts.replace('Z', '+00:00'))
                        days = max((latest_dt - prev_dt).total_seconds() / 86400.0, 1.0)

                        # Calculate total change since last run
                        total_click_change = max(0, current['clicks'] - int(p_clicks or 0))
                        total_fan_change = max(0, current['fans'] - int(p_fans or 0))
                        total_earn_change = current['earnings'] - float(p_earnings or 0)

                        # Average the changes across the number of days since last run
                        # This gives a normalized "daily rate" when scraper misses days
                        click_delta = int(round(total_click_change / days))
                        fan_delta = int(round(total_fan_change / days))
                        earn_delta = round(total_earn_change / days, 2)
                    else:
                        click_delta = 0
                        fan_delta = 0
                        earn_delta = 0.0

                changes[link] = {
                    'clicks_change': click_delta,
                    'fans_change': fan_delta,
                    'earnings_change': earn_delta,
                    'current_clicks': current['clicks'],
                    'current_fans': current['fans'],
                    'current_earnings': current['earnings']
                }

            return changes
    except Exception as e:
        print(f"Database error calculating changes: {e}")
        traceback.print_exc()
        return {}

def calculate_daily_changes_for_date(target_date):
    """Calculate changes for a specific date compared to the previous available data"""
    try:
        if not database_available:
            return {}

        db = TrackingDatabase()
        db.db_path = DATABASE_PATH

        target_date_str = target_date if isinstance(target_date, str) else target_date.strftime('%Y-%m-%d')

        with sqlite3.connect(db.db_path) as conn:
            cursor = conn.cursor()

            # Get OnlyMonster data for the target date
            cursor.execute("""
                SELECT link, clicks, fans, earnings, timestamp
                FROM onlymonster_data
                WHERE DATE(timestamp) = ?
                ORDER BY timestamp DESC
            """, (target_date_str,))

            current_data = {}
            for row in cursor.fetchall():
                link, clicks, fans, earnings, timestamp = row
                if link not in current_data:
                    current_data[link] = {
                        'clicks': int(clicks or 0),
                        'fans': int(fans or 0),
                        'earnings': float(earnings or 0),
                        'timestamp': timestamp
                    }

            if not current_data:
                return {}

            changes = {}

            # For each link, find the previous available data before the target date
            for link, current in current_data.items():
                cursor.execute("""
                    SELECT clicks, fans, earnings, timestamp
                    FROM onlymonster_data
                    WHERE link = ? AND DATE(timestamp) < ?
                    ORDER BY timestamp DESC
                    LIMIT 1
                """, (link, target_date_str))

                prev = cursor.fetchone()

                if prev:
                    p_clicks, p_fans, p_earnings, p_ts = prev
                    try:
                        prev_dt = datetime.fromisoformat(p_ts)
                        current_dt = datetime.fromisoformat(current['timestamp'])
                    except Exception:
                        prev_dt = datetime.fromisoformat(p_ts.replace('Z', '+00:00'))
                        current_dt = datetime.fromisoformat(current['timestamp'].replace('Z', '+00:00'))

                    days = max((current_dt - prev_dt).total_seconds() / 86400.0, 1.0)

                    # Calculate total change since last run
                    total_click_change = max(0, current['clicks'] - int(p_clicks or 0))
                    total_fan_change = max(0, current['fans'] - int(p_fans or 0))
                    total_earn_change = current['earnings'] - float(p_earnings or 0)

                    # Average the changes across the number of days
                    click_delta = int(round(total_click_change / days))
                    fan_delta = int(round(total_fan_change / days))
                    earn_delta = round(total_earn_change / days, 2)
                else:
                    click_delta = 0
                    fan_delta = 0
                    earn_delta = 0.0

                changes[link] = {
                    'clicks_change': click_delta,
                    'fans_change': fan_delta,
                    'earnings_change': earn_delta,
                    'current_clicks': current['clicks'],
                    'current_fans': current['fans'],
                    'current_earnings': current['earnings']
                }

            return changes
    except Exception as e:
        print(f"Database error calculating changes for date {target_date}: {e}")
        traceback.print_exc()
        return {}

def get_social_media_data():
    """Get social media data from the database (today's stored data)"""
    try:
        if not database_available:
            return get_live_social_media_data()

        # Get most recent available data from database first
        db = TrackingDatabase()
        # Override the database path for container environment
        db.db_path = DATABASE_PATH

        # Try to get the most recent data (check last few days)
        history_data = None
        for days_ago in range(3):  # Check today, yesterday, and day before
            check_date = (datetime.now() - timedelta(days=days_ago)).strftime('%Y-%m-%d')
            print(f"Looking for social media data for date: {check_date}")
            history_data = db.get_social_media_history(date=check_date)
            if history_data:
                print(f"Found {len(history_data)} records for {check_date}")
                break

        if not history_data:
            print("No data found for the last 3 days")

        if history_data:
            # Convert database data to API format
            results = {
                'summary': {
                    'total_posts_today': 0,
                    'total_accounts_with_posts': 0,
                    'total_accounts_checked': 0,
                    'tiktok_posts_today': 0,
                    'instagram_posts_today': 0,
                    'reddit_posts_today': 0
                },
                'tiktok': {'summary': {}, 'accounts_with_posts': []},
                'instagram': {'account_details': {}},
                'reddit': {'account_details': {}, 'total_removed_posts': 0}
            }

            platforms = {'tiktok': 0, 'instagram': 0, 'reddit': 0}
            active_accounts = 0

            for platform, username, post_count, removed_posts, post_times, removal_reasons, status, error_message, check_date in history_data:
                import json

                if post_count > 0:
                    active_accounts += 1

                platforms[platform] += post_count or 0
                results['summary']['total_posts_today'] += post_count or 0

                # Parse stored JSON data
                times = []
                if post_times:
                    try:
                        times = json.loads(post_times)
                    except:
                        times = []

                reasons = {}
                if removal_reasons:
                    try:
                        reasons = json.loads(removal_reasons)
                    except:
                        reasons = {}

                # Format platform-specific data
                if platform == 'tiktok':
                    if post_count > 0:
                        results['tiktok']['accounts_with_posts'].append(username)
                    results['tiktok']['summary'][username] = {
                        'posts_count': post_count or 0,
                        'post_times': times
                    }
                elif platform == 'instagram':
                    results['instagram']['account_details'][username] = {
                        'post_count': post_count or 0,
                        'post_times': times,
                        'error': error_message or ''
                    }
                elif platform == 'reddit':
                    results['reddit']['account_details'][username] = {
                        'post_count': post_count or 0,
                        'removed_count': removed_posts or 0,
                        'post_times': times,
                        'details': {'removal_reasons': reasons}
                    }
                    results['reddit']['total_removed_posts'] += removed_posts or 0

            results['summary']['total_accounts_with_posts'] = active_accounts
            results['summary']['total_accounts_checked'] = len(history_data)
            results['summary']['tiktok_posts_today'] = platforms['tiktok']
            results['summary']['instagram_posts_today'] = platforms['instagram']
            results['summary']['reddit_posts_today'] = platforms['reddit']

            # Get OnlyFans deal info
            try:
                deal_info = get_onlyfans_deal_info("foxnaomi")
            except Exception as e:
                print(f"OnlyFans checker not available: {e}")
                deal_info = {'has_deal': False, 'deal_text': '', 'deal_percentage': 0}

            # Create time window for display
            start_time = datetime.now() - timedelta(hours=24)
            end_time = datetime.now()

            return {
                'results': results,
                'deal_info': deal_info,
                'time_window': (start_time, end_time)
            }
        else:
            # Fall back to live data if no stored data
            return get_live_social_media_data()

    except Exception as e:
        print(f"Error getting stored social media data: {e}")
        print(f"Database available: {database_available}")
        traceback.print_exc()
        return get_live_social_media_data()

def get_live_social_media_data():
    """Get social media data from the live multi-platform checker"""
    try:
        # Try to import and use the actual social media checker
        sys.path.append(parent_dir)
        from social.multi_platform_checker import MultiPlatformChecker, get_24_hour_window

        # Get 24-hour window
        start_time, end_time = get_24_hour_window()

        # Check all platforms
        checker = MultiPlatformChecker(time_window=(start_time, end_time))
        results = checker.check_all_platforms()

        # Check OnlyFans deals
        deal_info = get_onlyfans_deal_info("foxnaomi")

        return {
            'results': results,
            'deal_info': deal_info,
            'time_window': (start_time, end_time)
        }
    except Exception as e:
        print(f"Error getting live social media data: {e}")
        traceback.print_exc()
        return {
            'results': {'summary': {'total_posts_today': 0, 'total_accounts_with_posts': 0}},
            'deal_info': {'has_deal': False, 'deal_text': '', 'deal_percentage': 0},
            'time_window': (datetime.now(), datetime.now())
        }

@app.route('/')
def dashboard():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/api/onlymonster/overview')
def onlymonster_overview():
    """Get OnlyMonster overview data"""
    try:
        from flask import request
        target_date = request.args.get('date')  # Optional date parameter

        # Calculate daily changes for the specified date or latest
        if target_date:
            changes = calculate_daily_changes_for_date(target_date)
            period_label = f"24 Hours ending {target_date}"
        else:
            changes = calculate_daily_changes()
            period_label = "Last 24 Hours"

        # Calculate totals
        total_fans_change = sum(data.get('fans_change', 0) for data in changes.values())
        total_clicks_change = sum(data.get('clicks_change', 0) for data in changes.values())
        total_earnings = sum(data.get('current_earnings', 0) for data in changes.values())
        total_earnings_change = sum(data.get('earnings_change', 0) for data in changes.values())
        active_links = len([l for l, d in changes.items() if d.get('fans_change', 0) > 0])

        return jsonify({
            'period': period_label,
            'days_count': 1,
            'total_fans': total_fans_change,
            'total_clicks': total_clicks_change,
            'total_revenue': total_earnings,
            'total_earnings_change': total_earnings_change,  # kept for backward compatibility if needed
            'active_links': active_links,
            'total_links': len(PRIORITY_LINKS),
            'selected_date': target_date
        })
    except Exception as e:
        print(f"Error in overview: {e}")
        traceback.print_exc()

@app.route('/api/onlymonster/revenue-dashboard')
def onlymonster_revenue_dashboard():
    """Return latest OnlyMonster dashboard revenue metrics scraped and stored by the scraper."""
    try:
        db = TrackingDatabase()
        row = db.get_latest_om_dashboard_metrics()
        if not row:
            return jsonify({'error': 'No revenue dashboard metrics available yet'}), 404
        total, sales, subs, arppu, ts = row
        return jsonify({
            'total_revenue': total,
            'sales_revenue': sales,
            'subscription_revenue': subs,
            'arppu': arppu,
            'timestamp': ts
        })
    except Exception as e:
        print(f"Error in revenue dashboard api: {e}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

        return jsonify({'error': str(e)}), 500

@app.route('/api/onlymonster/metrics')
def onlymonster_metrics():
    """Get detailed OnlyMonster metrics"""
    try:
        from flask import request
        target_date = request.args.get('date')  # Optional date parameter

        # Calculate daily changes for the specified date or latest
        if target_date:
            changes = calculate_daily_changes_for_date(target_date)
        else:
            changes = calculate_daily_changes()

        # Format metrics for frontend, prioritizing high performers
        formatted_metrics = []
        priority_set = set(PRIORITY_LINKS)

        # Sort by priority first, then by fan growth
        sorted_changes = sorted(changes.items(),
                              key=lambda x: (x[0] not in priority_set, -x[1].get('fans_change', 0)))

        for link, data in sorted_changes:
            fans_change = data.get('fans_change', 0)
            clicks_change = data.get('clicks_change', 0)
            current_fans = data.get('current_fans', 0)
            current_clicks = data.get('current_clicks', 0)

            # Calculate daily conversion rate (if there were clicks in the last 24h)
            daily_conversion_rate = (fans_change / clicks_change * 100) if clicks_change > 0 else 0

            # Calculate average conversion rate (total fans / total clicks)
            avg_conversion_rate = (current_fans / current_clicks * 100) if current_clicks > 0 else 0

            formatted_metrics.append({
                'name': link,
                'fan_growth': fans_change,
                'click_growth': clicks_change,
                'current_fans': current_fans,
                'current_clicks': current_clicks,
                'daily_conversion_rate': round(daily_conversion_rate, 2),
                'avg_conversion_rate': round(avg_conversion_rate, 2),
                'is_priority': link in priority_set,
                'earnings_change': data.get('earnings_change', 0),
                'current_earnings': data.get('current_earnings', 0)
            })

        return jsonify({'metrics': formatted_metrics})
    except Exception as e:
        print(f"Error in metrics: {e}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/onlymonster/earnings')
def onlymonster_earnings():
    """Get earnings breakdown"""
    try:
        latest_data = get_latest_metrics()
        changes = calculate_daily_changes()

        earnings_data = []
        total_daily_change = 0

        for link_name, clicks, fans, earnings, timestamp in latest_data:
            if link_name in changes:
                daily_change = changes[link_name].get('earnings_change', 0)
                total_daily_change += daily_change

                earnings_per_fan = earnings / fans if fans > 0 else 0

                earnings_data.append({
                    'name': link_name,
                    'current_earnings': earnings,
                    'daily_change': daily_change,
                    'earnings_per_fan': round(earnings_per_fan, 4),
                    'fans': fans,
                    'is_priority': link_name in PRIORITY_LINKS
                })

        # Sort by current earnings
        earnings_data.sort(key=lambda x: x['current_earnings'], reverse=True)

        return jsonify({
            'earnings_data': earnings_data,
            'total_daily_change': round(total_daily_change, 2)
        })
    except Exception as e:
        print(f"Error in earnings: {e}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/social/overview')
def social_overview():
    """Get social media overview"""
    try:
        social_data = get_social_media_data()
        results = social_data['results']
        deal_info = social_data['deal_info']
        start_time, end_time = social_data['time_window']

        return jsonify({
            'time_window': f"{start_time.strftime('%Y-%m-%d %H:%M')} to {end_time.strftime('%Y-%m-%d %H:%M')} Central",
            'tiktok_posts': results['summary'].get('tiktok_posts_today', 0),
            'instagram_posts': results['summary'].get('instagram_posts_today', 0),
            'reddit_posts': results['summary'].get('reddit_posts_today', 0),
            'reddit_removed_posts': results.get('reddit', {}).get('total_removed_posts', 0),
            'total_posts': results['summary'].get('total_posts_today', 0),
            'accounts_with_posts': results['summary'].get('total_accounts_with_posts', 0),
            'total_accounts': results['summary'].get('total_accounts_checked', 0),
            'onlyfans_deal': {
                'has_deal': deal_info.get('has_deal', False),
                'deal_text': deal_info.get('deal_text', ''),
                'deal_percentage': deal_info.get('deal_percentage', 0)
            }
        })
    except Exception as e:
        print(f"Error in social overview: {e}")
        traceback.print_exc()
        return jsonify({
            'time_window': "Data unavailable",
            'tiktok_posts': 0,
            'instagram_posts': 0,
            'reddit_posts': 0,
            'reddit_removed_posts': 0,
            'total_posts': 0,
            'accounts_with_posts': 0,
            'total_accounts': 0,
            'onlyfans_deal': {'has_deal': False, 'deal_text': '', 'deal_percentage': 0}
        })

@app.route('/api/social/details')
def social_details():
    """Get detailed social media account information"""
    try:
        social_data = get_social_media_data()
        results = social_data['results']

        # Format account details
        accounts = {
            'tiktok': [],
            'instagram': [],
            'reddit': []
        }

        # Process TikTok accounts - show all accounts, not just those with posts
        tiktok_data = results.get('tiktok', {})
        if 'summary' in tiktok_data:
            for username, summary in tiktok_data['summary'].items():
                accounts['tiktok'].append({
                    'username': f"@{username}",
                    'posts': summary.get('posts_count', 0),
                    'post_times': summary.get('post_times', [])[:5],
                    'status': 'active' if summary.get('posts_count', 0) > 0 else 'inactive'
                })

        # Process Instagram accounts
        instagram_data = results.get('instagram', {})
        if 'account_details' in instagram_data:
            for username, details in instagram_data['account_details'].items():
                accounts['instagram'].append({
                    'username': f"@{username}",
                    'posts': details.get('post_count', 0),
                    'post_times': details.get('post_times', [])[:5],
                    'status': 'active' if details.get('post_count', 0) > 0 else 'inactive',
                    'error': details.get('error', '')
                })

        # Process Reddit accounts
        reddit_data = results.get('reddit', {})
        if 'account_details' in reddit_data:
            for username, details in reddit_data['account_details'].items():
                post_count = details.get('post_count', 0)
                removed_count = details.get('removed_count', 0)

                accounts['reddit'].append({
                    'username': username,
                    'posts': post_count,
                    'removed_posts': removed_count,
                    'post_times': details.get('post_times', [])[:5],
                    'removal_reasons': details.get('details', {}).get('removal_reasons', {}),
                    'status': 'active' if post_count > 0 else ('partial' if removed_count > 0 else 'inactive')
                })

        return jsonify({'accounts': accounts})
    except Exception as e:
        print(f"Error in social details: {e}")
        traceback.print_exc()
        return jsonify({'accounts': {'tiktok': [], 'instagram': [], 'reddit': []}})

@app.route('/api/debug/database')
def debug_database():
    """Debug endpoint to check database connectivity"""
    db_path = get_database_path()
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM tracking_data")
            count = cursor.fetchone()[0]

            cursor.execute("SELECT tracking_link_name, COUNT(*) as records FROM tracking_data GROUP BY tracking_link_name ORDER BY records DESC LIMIT 5")
            top_links = cursor.fetchall()

            # Freshness checks
            cursor.execute("SELECT MAX(timestamp) FROM tracking_data")
            latest_ts_row = cursor.fetchone()
            latest_ts = latest_ts_row[0] if latest_ts_row else None
            freshness_hours = None
            if latest_ts:
                try:
                    latest_dt = datetime.fromisoformat(latest_ts.replace('Z', '+00:00') if 'Z' in latest_ts else latest_ts)
                    freshness_hours = round((datetime.now() - latest_dt).total_seconds() / 3600, 2)
                except Exception:
                    pass

            return jsonify({
                'database_path': db_path,
                'database_exists': os.path.exists(db_path),
                'total_records': count,
                'top_links': top_links,
                'latest_timestamp': latest_ts,
                'hours_since_last_record': freshness_hours
            })
    except Exception as e:
        return jsonify({
            'database_path': db_path,
            'database_exists': os.path.exists(db_path),
            'error': str(e)
        })

@app.route('/api/social/history/<date>')
def social_history_by_date(date):
    """Get social media history for a specific date"""
    if not database_available:
        return jsonify({'error': 'Historical data not available'}), 500

    try:
        db = TrackingDatabase()
        db.db_path = DATABASE_PATH
        history_data = db.get_social_media_history(date=date)

        # Format data for frontend
        formatted_accounts = {
            'tiktok': [],
            'instagram': [],
            'reddit': []
        }

        for platform, username, post_count, removed_posts, post_times, removal_reasons, status, error_message, check_date in history_data:
            import json

            account_data = {
                'username': f"@{username}" if platform != 'reddit' else username,
                'posts': post_count or 0,
                'status': status or 'inactive'
            }

            # Add post times if available
            if post_times:
                try:
                    times = json.loads(post_times)
                    account_data['post_times'] = times[:5]  # Limit to first 5
                except:
                    account_data['post_times'] = []
            else:
                account_data['post_times'] = []

            # Add platform-specific data
            if platform == 'reddit':
                account_data['removed_posts'] = removed_posts or 0
                if removal_reasons:
                    try:
                        account_data['removal_reasons'] = json.loads(removal_reasons)
                    except:
                        account_data['removal_reasons'] = {}
                else:
                    account_data['removal_reasons'] = {}
            # Add error message for all platforms if available
            if error_message:
                account_data['error'] = error_message

            formatted_accounts[platform].append(account_data)

        return jsonify({
            'date': date,
            'accounts': formatted_accounts
        })

    except Exception as e:
        print(f"Error getting social history for {date}: {e}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/onlymonster/available-dates')
def available_onlymonster_dates():
    """Get list of dates that have OnlyMonster tracking data"""
    try:
        with sqlite3.connect(DATABASE_PATH) as conn:
            cursor = conn.cursor()

            # Get unique dates from tracking_data table
            cursor.execute('''
                SELECT DISTINCT DATE(timestamp) as date
                FROM tracking_data
                WHERE timestamp IS NOT NULL
                ORDER BY date DESC
                LIMIT 30
            ''')

            dates = [row[0] for row in cursor.fetchall()]
            return jsonify({'dates': dates})

    except Exception as e:
        print(f"Error getting available OnlyMonster dates: {e}")
        return jsonify({'dates': [], 'error': str(e)})

@app.route('/api/last-updated')
def get_last_updated():
    """Get the timestamp of the last database update"""
    try:
        db_path = get_database_path()
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()

            # Get the latest timestamp from tracking_data table
            cursor.execute('''
                SELECT MAX(timestamp) as latest_update
                FROM tracking_data
            ''')

            result = cursor.fetchone()
            if result and result[0]:
                latest_timestamp = result[0]

                # Parse the timestamp and return as ISO format for JavaScript
                try:
                    dt = datetime.fromisoformat(latest_timestamp)
                    return jsonify({
                        'last_updated': dt.isoformat(),
                        'formatted': dt.strftime('%Y-%m-%d %H:%M:%S'),
                        'success': True
                    })
                except Exception as parse_error:
                    print(f"Error parsing timestamp {latest_timestamp}: {parse_error}")
                    return jsonify({
                        'last_updated': latest_timestamp,
                        'formatted': latest_timestamp,
                        'success': True
                    })
            else:
                return jsonify({
                    'error': 'No data found in database',
                    'success': False
                })

    except Exception as e:
        print(f"Error getting last updated timestamp: {e}")
        return jsonify({
            'error': str(e),
            'success': False
        }), 500

@app.route('/robots.txt')
def robots_txt():
    """Serve robots.txt to prevent search engine indexing"""
    return app.send_static_file('robots.txt')

@app.route('/api/social/available-dates')
def available_social_dates():
    """Get list of dates that have social media history data"""
    if not database_available:
        return jsonify({'dates': []})

    try:
        db = TrackingDatabase()
        db.db_path = DATABASE_PATH
        dates = db.get_available_social_dates()
        return jsonify({'dates': dates})

    except Exception as e:
        print(f"Error getting available social dates: {e}")
        return jsonify({'dates': [], 'error': str(e)})

@app.route('/api/scrape/trigger', methods=['POST'])
def trigger_scraping():
    """Trigger a new scraping session"""
    try:
        # Try to import and run the scraper
        try:
            from core.onlymonster_scraper import OnlyMonsterScraper

            scraper = OnlyMonsterScraper()
            data = scraper.run_scraping()

            return jsonify({
                'success': True,
                'message': 'Scraping completed successfully',
                'data_collected': len(data) if data else 0
            })
        except ImportError as e:
            print(f"Scraper module not available: {e}")
            return jsonify({
                'success': False,
                'message': 'Scraping functionality not available in web container. Please run scraping externally.'
            }), 503
        except Exception as e:
            print(f"Scraping failed: {e}")
            return jsonify({
                'success': False,
                'message': f'Scraping failed: {str(e)}. This is expected in the web container - run scraping externally.'
            }), 503

    except Exception as e:
        print(f"Scraping trigger failed: {e}")
        return jsonify({
            'success': False,
            'message': f'Failed to trigger scraping: {str(e)}'
        }), 500

@app.route('/api/analytics/trending/fan-growth')
def trending_fan_growth():
    """Get trending analysis for fan growth over time"""
    try:
        from core.analytics import TrackingAnalytics

        analytics = TrackingAnalytics()

        # Get data directly without AI analysis to avoid timeouts
        current_data = analytics.get_latest_data()
        daily_previous_data = analytics.get_previous_data(24)  # Last 24 hours
        weekly_previous_data = analytics.get_previous_data(24 * 7)  # Last 7 days

        # Calculate changes manually without AI
        daily_changes = analytics.calculate_changes(current_data, daily_previous_data)
        weekly_changes = analytics.calculate_changes(current_data, weekly_previous_data)

        # Extract fan growth data
        daily_fan_growth = daily_changes['new_fans']
        weekly_fan_growth = weekly_changes['new_fans']

        # Calculate total growth
        total_daily_growth = sum(daily_fan_growth.values())
        total_weekly_growth = sum(weekly_fan_growth.values())

        # Get top performing links
        top_daily_links = sorted(daily_fan_growth.items(), key=lambda x: x[1], reverse=True)[:5]
        top_weekly_links = sorted(weekly_fan_growth.items(), key=lambda x: x[1], reverse=True)[:5]

        return jsonify({
            'time_periods': {
                'daily': {
                    'total_growth': total_daily_growth,
                    'top_links': [{'name': name, 'growth': growth} for name, growth in top_daily_links]
                },
                'weekly': {
                    'total_growth': total_weekly_growth,
                    'top_links': [{'name': name, 'growth': growth} for name, growth in top_weekly_links]
                }
            },
            'daily_changes': daily_changes,
            'weekly_changes': weekly_changes
        })

    except Exception as e:
        print(f"Trending analysis failed: {e}")
        return jsonify({
            'error': f'Trending analysis failed: {str(e)}'
        }), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
