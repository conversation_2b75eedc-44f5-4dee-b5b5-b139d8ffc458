#!/usr/bin/env bash
set -euo pipefail

# Deploy the dashboard container and verify DB connectivity.
# Usage: HOST_DATA_DIR=/abs/path/to/onlymonster-automations ./deploy_dashboard.sh

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

: "${HOST_DATA_DIR:=${SCRIPT_DIR%/web_dashboard}}"

echo "[deploy] Using HOST_DATA_DIR=$HOST_DATA_DIR"
if [[ ! -d "$HOST_DATA_DIR" ]]; then
  echo "[deploy] ERROR: HOST_DATA_DIR does not exist: $HOST_DATA_DIR" >&2
  exit 1
fi

echo "[deploy] Bringing up dashboard with docker compose..."
docker compose up -d --build --remove-orphans

echo "[deploy] Waiting for container to be healthy..."
sleep 3
docker ps --filter name=cheeksdash

echo "[deploy] Checking API from inside the container..."
docker compose exec -T cheeksdash curl -s http://localhost:5000/api/debug/database || true

cat <<EOM

Next steps:
- Map DNS/Traefik to cheeksdash.com if not already.
- Verify externally:
  curl -s https://cheeksdash.com/api/debug/database | jq

EOM

