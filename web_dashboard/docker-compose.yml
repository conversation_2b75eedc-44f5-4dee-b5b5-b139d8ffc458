version: '3.8'

services:
  cheeksdash:
    build: .
    container_name: cheeksdash
    restart: unless-stopped
    environment:
      - FLASK_ENV=production
      - DATABASE_PATH=/app/data/onlymonster_data.db
    volumes:
      - ${HOST_DATA_DIR:-/root/onlymonster-automations}:/app/data:ro
    networks:
      - coolify
    labels:
      # Traefik configuration for automatic SSL and routing
      - "traefik.enable=true"
      - "traefik.http.routers.cheeksdash.rule=Host(`cheeksdash.com`) || Host(`www.cheeksdash.com`)"
      - "traefik.http.routers.cheeksdash.entrypoints=https"
      - "traefik.http.routers.cheeksdash.tls.certresolver=letsencrypt"
      - "traefik.http.routers.cheeksdash.tls=true"
      - "traefik.http.services.cheeksdash.loadbalancer.server.port=5000"
      # Redirect www to non-www
      - "traefik.http.routers.cheeksdash-www.rule=Host(`www.cheeksdash.com`)"
      - "traefik.http.routers.cheeksdash-www.entrypoints=https"
      - "traefik.http.routers.cheeksdash-www.tls.certresolver=letsencrypt"
      - "traefik.http.routers.cheeksdash-www.tls=true"
      - "traefik.http.routers.cheeksdash-www.middlewares=www-redirect"
      - "traefik.http.middlewares.www-redirect.redirectregex.regex=^https://www\\.(.+)"
      - "traefik.http.middlewares.www-redirect.redirectregex.replacement=https://$$1"
      - "traefik.http.middlewares.www-redirect.redirectregex.permanent=true"
      # HTTP to HTTPS redirect
      - "traefik.http.routers.cheeksdash-http.rule=Host(`cheeksdash.com`) || Host(`www.cheeksdash.com`)"
      - "traefik.http.routers.cheeksdash-http.entrypoints=http"
      - "traefik.http.routers.cheeksdash-http.middlewares=https-redirect"
      - "traefik.http.middlewares.https-redirect.redirectscheme.scheme=https"
      - "traefik.http.middlewares.https-redirect.redirectscheme.permanent=true"

networks:
  coolify:
    external: true
