# Gunicorn configuration file
bind = "0.0.0.0:5000"
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
user = "root"
group = "root"
tmp_upload_dir = None
errorlog = "/var/log/cheeksdash/error.log"
accesslog = "/var/log/cheeksdash/access.log"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
loglevel = "info"