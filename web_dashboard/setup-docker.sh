#!/bin/bash
# Cheeks Dashboard Setup Script for Coolify/Docker Environment

echo "🚀 Setting up Cheeks Dashboard for Docker/Coolify..."

# Check if we're in the right directory
if [[ ! -f "docker-compose.yml" ]]; then
    echo "❌ Error: Please run this script from the web_dashboard directory"
    exit 1
fi

# Check if Coolify network exists
if ! docker network ls | grep -q "coolify"; then
    echo "❌ Error: Coolify network not found. Make sure Coolify is running."
    exit 1
fi

echo "📦 Installing Flask dependencies..."
cd /root/onlymonster-automations
source venv/bin/activate
pip install -r web_dashboard/requirements.txt

echo "🐳 Building Docker image..."
cd web_dashboard
docker build -t cheeksdash:latest .

echo "🔧 Stopping existing container (if any)..."
docker stop cheeksdash 2>/dev/null || true
docker rm cheeksdash 2>/dev/null || true

echo "🚀 Starting Cheeks Dashboard container..."
docker-compose up -d

echo "⏳ Waiting for container to be ready..."
sleep 10

# Check if container is running
if docker ps | grep -q "cheeksdash"; then
    echo "✅ Container is running!"
else
    echo "❌ Container failed to start. Checking logs..."
    docker logs cheeksdash
    exit 1
fi

echo "📊 Testing dashboard endpoints..."
if curl -f -s http://localhost:5000/ > /dev/null; then
    echo "✅ Dashboard is responding!"
else
    echo "⚠️  Dashboard not responding on localhost, but may work through Traefik"
fi

echo ""
echo "✅ Setup complete!"
echo ""
echo "🌐 DNS Configuration Required:"
echo "Set these DNS records for cheeksdash.com:"
echo "  Type: A, Name: @, Value: ***********"
echo "  Type: A, Name: www, Value: ***********"
echo ""
echo "🔄 After DNS propagates (24-48 hours):"
echo "  Your dashboard will be available at: https://cheeksdash.com"
echo "  SSL certificates will be automatically provisioned by Traefik"
echo ""
echo "📋 Management commands:"
echo "  View logs: docker logs cheeksdash -f"
echo "  Restart: docker-compose restart"
echo "  Stop: docker-compose down"
echo "  Rebuild: docker-compose up --build -d"