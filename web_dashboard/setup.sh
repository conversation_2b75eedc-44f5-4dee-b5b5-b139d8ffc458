#!/bin/bash
# Cheeks Dashboard Setup Script

echo "🚀 Setting up Cheeks Dashboard..."

# Create log directories
echo "📁 Creating log directories..."
sudo mkdir -p /var/log/cheeksdash
sudo mkdir -p /var/log/nginx
sudo chown root:root /var/log/cheeksdash

# Install dependencies
echo "📦 Installing Flask dependencies..."
cd /root/onlymonster-automations
source venv/bin/activate
pip install -r web_dashboard/requirements.txt

# Install nginx if not present
echo "🔧 Checking nginx installation..."
if ! command -v nginx &> /dev/null; then
    echo "Installing nginx..."
    sudo apt update
    sudo apt install -y nginx
fi

# Copy nginx configuration
echo "⚙️ Setting up nginx configuration..."
sudo cp web_dashboard/nginx.conf /etc/nginx/sites-available/cheeksdash.com
sudo ln -sf /etc/nginx/sites-available/cheeksdash.com /etc/nginx/sites-enabled/
sudo nginx -t

# Install systemd service
echo "🔄 Setting up systemd service..."
sudo cp web_dashboard/systemd.service /etc/systemd/system/cheeksdash.service
sudo systemctl daemon-reload
sudo systemctl enable cheeksdash.service

# Install certbot for SSL if not present
echo "🔒 Checking SSL certificate setup..."
if ! command -v certbot &> /dev/null; then
    echo "Installing certbot..."
    sudo apt install -y certbot python3-certbot-nginx
fi

echo "✅ Setup complete!"
echo ""
echo "🌐 Next steps:"
echo "1. Configure your DNS to point cheeksdash.com to this server's IP"
echo "2. Run: sudo certbot --nginx -d cheeksdash.com -d www.cheeksdash.com"
echo "3. Start the service: sudo systemctl start cheeksdash.service"
echo "4. Restart nginx: sudo systemctl restart nginx"
echo ""
echo "📊 Your dashboard will be available at: https://cheeksdash.com"