/* Enhanced Cheeks Dashboard CSS */
:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;

    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-card: #16213e;
    --bg-card-hover: #1e2746;

    --text-primary: #ffffff;
    --text-secondary: #a1a1aa;
    --text-muted: #71717a;

    --border-color: #27272a;
    --border-accent: #3f3f46;

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);

    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
    overflow-x: hidden;
}

.dashboard {
    min-height: 100vh;
    padding: 0;
}

/* Header */
.header {
    background: linear-gradient(135deg, var(--bg-card) 0%, var(--bg-secondary) 100%);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
    min-height: 60px;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.header h1 {
    color: var(--text-primary);
    font-size: 1.75rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header h1 i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.last-updated {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.refresh-all-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-sm);
}

.refresh-all-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.refresh-all-btn:active {
    transform: translateY(0);
}

/* Main content */
.overview-section,
.analytics-section {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem 1.5rem;
}

.overview-section h2,
.analytics-section h2 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.overview-section h2 i,
.analytics-section h2 i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.date-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.date-selector label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.date-selector select {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    color: var(--text-primary);
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    outline: none;
    transition: all 0.2s ease;
}

.date-selector select:hover {
    border-color: var(--border-accent);
    background: var(--bg-card-hover);
}

.date-selector select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Grid layouts */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 1.5rem;
}

.content-grid {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1.5rem 2rem;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    align-items: start;
}

.analytics-section.wide {
    grid-column: 1 / -1;
}

/* Cards */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    background: var(--bg-card-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.02);
}

.card-header h3 {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-header i {
    color: var(--primary-color);
}

.card-content {
    padding: 1rem 1.25rem;
}

.card-footer {
    padding: 0.5rem 1.25rem 0.75rem;
    border-top: 1px solid var(--border-color);
    background: rgba(255, 255, 255, 0.02);
}

.period {
    color: var(--text-muted);
    font-size: 0.75rem;
    background: var(--bg-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: 500;
}

/* Deal Banner */
.deal-banner-section {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem 1.5rem;
}

.deal-banner {
    background: linear-gradient(135deg, #ef4444, #dc2626, #b91c1c);
    border-radius: 1rem;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    position: relative;
}

.deal-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.deal-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 2rem;
    position: relative;
    z-index: 1;
    color: white;
}

.deal-icon {
    font-size: 3rem;
    color: #fbbf24;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.deal-text {
    flex: 1;
}

.deal-text h2 {
    font-size: 2rem;
    font-weight: 900;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    letter-spacing: 0.1em;
}

.deal-text p {
    font-size: 1.125rem;
    margin: 0.5rem 0 0;
    opacity: 0.9;
    font-weight: 500;
}

.deal-action {
    font-size: 2.5rem;
    color: #fbbf24;
}

.pulse {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Social Activity Section */
.social-activity-section {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1.5rem;
}

.date-selector select {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    cursor: pointer;
    min-width: 150px;
}

.date-selector select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.social-overview-cards {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    align-items: start;
}

.social-summary-card {
    background: linear-gradient(135deg, var(--bg-card) 0%, #2a1a38 100%);
    border: 1px solid #4c2d75;
}

.social-stats {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
}

.social-platforms-overview {
    display: grid;
    gap: 1.5rem;
}

/* Overview cards */
.onlymonster-card {
    background: linear-gradient(135deg, var(--bg-card) 0%, #1a2238 100%);
    border: 1px solid #2d3748;
}

.card-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    padding: 0.75rem 1.25rem 1rem;
}

.stat {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.375rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.1rem;
    line-height: 1.1;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat.highlight .stat-value {
    color: var(--success-color);
    font-size: 1.5rem;
}

.stat.earnings .stat-value {
    color: #10b981;
}

.stat.warning .stat-value {
    color: var(--warning-color);
}

.footer-stat {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* OnlyFans deal status */
.onlyfans-deal-status {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.onlyfans-deal-status.active {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.onlyfans-deal-status.inactive {
    color: var(--text-muted);
}

.onlyfans-deal-status i {
    color: #ef4444;
    font-size: 1rem;
}

/* Metrics table */
.metrics-table-wrapper {
    overflow-x: auto;
}

.metrics-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.metrics-table th {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.75rem;
}

.metrics-table td {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
}

.metrics-table tr:hover {
    background: rgba(255, 255, 255, 0.02);
}

/* Sortable table headers */
.metrics-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: background-color 0.2s ease;
}

.metrics-table th.sortable:hover {
    background: rgba(255, 255, 255, 0.1);
}

.sort-icon {
    margin-left: 0.5rem;
    opacity: 0.5;
    font-size: 0.75rem;
    transition: opacity 0.2s ease;
}

.sort-icon.active {
    opacity: 1;
    color: var(--primary-color);
}

.metrics-table th.sortable:hover .sort-icon {
    opacity: 0.8;
}

.priority-row {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
    border-left: 3px solid var(--primary-color);
}

.priority-icon {
    color: var(--warning-color);
    margin-right: 0.5rem;
}


/* Ensure thumbtack sits to the left of the name inline, not above */
.link-name {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Rotate thumbtack slightly for visual cue */
.priority-icon.fa-thumbtack {
    transform: rotate(20deg);
}

.link-name {
    font-weight: 500;
    max-width: 200px;
}

.truncate {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.metric-value.positive {
    color: var(--success-color);
    font-weight: 600;
}

.metric-value.negative {
    color: var(--danger-color);
    font-weight: 600;
}

.current-totals {
    font-size: 0.8125rem;
}

.current-totals .subdued {
    color: var(--text-muted);
}

.earnings-cell {
    text-align: right;
}

.earnings-change {
    font-size: 0.75rem;
    font-weight: 500;
}

.earnings-change.positive {
    color: var(--success-color);
}

.earnings-change.negative {
    color: var(--danger-color);
}

/* Header controls */
.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.legend {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--text-muted);
}

.legend-item.priority i {
    color: var(--warning-color);
}

.legend-item.positive i {
    color: var(--success-color);
}

.legend-item.negative i {
    color: var(--danger-color);
}

/* Social platforms */
.social-platforms {
    display: grid;
    gap: 1.5rem;
}

.platform-card {
    border-radius: 0.75rem;
}

.platform-card.tiktok {
    border-left: 4px solid #ff0050;
}

.platform-card.instagram {
    border-left: 4px solid #e4405f;
}

.platform-card.reddit {
    border-left: 4px solid #ff4500;
}

.post-count {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.accounts-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.account-item {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 0.5rem;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.account-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: var(--border-accent);
}

.account-item.active {
    border-color: var(--success-color);
}

.account-item.partial {
    border-color: var(--warning-color);
}

.account-item.inactive {
    opacity: 0.6;
}

.account-item.error {
    border-color: var(--danger-color);
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.05));
}

.account-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.username {
    font-weight: 500;
    color: var(--text-primary);
}

.reddit-stats {
    display: flex;
    gap: 0.75rem;
    font-size: 0.75rem;
}

.removed-count {
    color: var(--danger-color);
    font-weight: 500;
}

.post-times {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.time-badge {
    background: var(--bg-secondary);
    color: var(--text-muted);
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
}

.removal-reasons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 0.5rem;
}

.reason-badge {
    background: var(--danger-color);
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
}

.no-activity {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 1rem;
}

.error-text {
    color: var(--danger-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.last-post-time {
    color: var(--text-muted);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    font-style: italic;
}

/* Earnings list */
.earnings-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.earnings-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.earnings-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.rank {
    background: var(--gradient-primary);
    color: white;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-weight: 600;
    font-size: 0.75rem;
    flex-shrink: 0;
}

.earning-details {
    flex: 1;
}

.earning-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.earning-name.priority {
    color: var(--warning-color);
}

.earning-name i {
    margin-right: 0.25rem;
}

.earning-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
}

.daily-change {
    font-weight: 500;
}

.per-fan {
    color: var(--text-muted);
}

.earnings-summary {
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.summary-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.summary-stat .label {
    color: var(--text-secondary);
    font-weight: 500;
}

.summary-stat .value {
    font-weight: 600;
    font-size: 1.125rem;
}

/* Loading states */
.loading {
    text-align: center;
    color: var(--text-muted);
    padding: 2rem;
}

.loading-row td {
    text-align: center;
    color: var(--text-muted);
    padding: 2rem;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(15, 15, 35, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.loading-spinner {
    text-align: center;
    color: var(--text-primary);
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Error handling */
.error-toast {
    position: fixed;
    top: 1rem;
    right: 1rem;
    background: var(--gradient-danger);
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    display: none;
    align-items: center;
    gap: 0.75rem;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    max-width: 400px;
}

.error-toast button {
    background: none;
    border: none;
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.error {
    color: var(--danger-color);
    text-align: center;
    font-style: italic;
}

/* Debug section */
.debug-section {
    max-width: 1400px;
    margin: 2rem auto;
    padding: 0 1.5rem;
}

.debug-section pre {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    font-size: 0.75rem;
    line-height: 1.5;
}

/* Responsive design */
@media (max-width: 1200px) {
    .content-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .social-overview-cards {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 900px) {
    .card-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
}

@media (max-width: 768px) {
    .header {
        padding: 0.25rem 0;
        min-height: 50px;
    }

    .header-content {
        padding: 0.5rem 1rem;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: 0.5rem;
        min-height: 50px;
    }

    .header h1 {
        font-size: 1.125rem;
        font-weight: 600;
        line-height: 1.2;
    }

    .header h1 i {
        font-size: 1.125rem;
        margin-right: 0.375rem;
    }

    .header-actions {
        flex-direction: row;
        gap: 0.375rem;
        align-items: center;
    }

    .header-controls {
        gap: 0.375rem;
    }

    .header-controls .btn,
    .refresh-all-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        min-height: auto;
        line-height: 1.2;
    }

    .last-updated {
        font-size: 0.7rem;
        line-height: 1.2;
    }
}

    .deal-banner-section {
        padding: 0.75rem 1rem 0.5rem;
    }

    .deal-content {
        flex-direction: row;
        align-items: center;
        text-align: left;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
    }

    .deal-text h2 {
        font-size: 1.125rem;
        font-weight: 800;
        line-height: 1.1;
        margin: 0;
        letter-spacing: 0.05em;
    }

    .deal-text p {
        font-size: 0.8rem;
        margin: 0.125rem 0 0;
        opacity: 0.85;
        line-height: 1.2;
    }

    .deal-icon,
    .deal-action {
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    .deal-text {
        flex: 1;
        min-width: 0;
    }

    .social-overview-cards {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* Compact activity summary on mobile */
    .social-summary-card .card-header {
        padding: 1rem 1.25rem 0.75rem;
    }

    .social-summary-card .card-header h3 {
        font-size: 1rem;
        line-height: 1.2;
    }

    .social-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        padding: 1rem 1.25rem 1.25rem;
    }

    .stat {
        text-align: center;
    }

    .stat-value {
        font-size: 1.25rem;
        margin-bottom: 0.1rem;
        line-height: 1.05;
    }

    .stat.highlight .stat-value {
        font-size: 1.4rem;
    }

    .stat-label {
        font-size: 0.7rem;
        line-height: 1.2;
    }

    .cards-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .card-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.625rem;
        padding: 0.75rem 1rem;
    }

    .overview-section,
    .analytics-section,
    .social-activity-section,
    .deal-banner-section {
        padding: 0.875rem 1rem 0.625rem;
    }

    /* Compact social activity section header */
    .social-activity-section h2 {
        font-size: 1.25rem;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .date-selector select {
        padding: 0.375rem 0.5rem;
        font-size: 0.8rem;
    }

    .content-grid {
        padding: 0 1rem 2rem;
    }

    .metrics-table-wrapper {
        margin: -1.5rem;
        padding: 1.5rem;
    }

    .metrics-table {
        font-size: 0.75rem;
    }

    .metrics-table th,
    .metrics-table td {
        padding: 0.5rem 0.25rem;
    }

    .link-name {
        max-width: 120px;
    }

    .earning-stats {
        flex-direction: column;
        gap: 0.25rem;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 0.125rem 0;
        min-height: 45px;
    }

    .header-content {
        padding: 0.375rem 0.75rem;
        gap: 0.25rem;
        min-height: 45px;
    }

    .header h1 {
        font-size: 1rem;
        font-weight: 600;
        line-height: 1.1;
    }

    .header h1 i {
        font-size: 1rem;
        margin-right: 0.25rem;
    }

    .header-actions {
        gap: 0.25rem;
        flex-shrink: 0;
    }

    .header-controls .btn,
    .refresh-all-btn {
        padding: 0.1875rem 0.375rem;
        font-size: 0.75rem;
        border-radius: 0.25rem;
        line-height: 1.1;
    }

    /* Hide button text on very small screens, keep only icons */
    .header-controls .btn .btn-text,
    .refresh-all-btn .btn-text {
        display: none;
    }

    .header-controls .btn i,
    .refresh-all-btn i {
        margin-right: 0;
        font-size: 0.875rem;
    }

    /* Compact last updated text */
    .last-updated {
        font-size: 0.65rem;
        color: var(--text-muted);
        line-height: 1.1;
    }

    /* Extra compact deal banner for small screens */
    .deal-banner-section {
        padding: 0.5rem 0.75rem 0.375rem;
    }

    .deal-content {
        padding: 0.5rem 0.75rem;
        gap: 0.375rem;
    }

    .deal-text h2 {
        font-size: 1rem;
        font-weight: 800;
        line-height: 1.05;
        letter-spacing: 0.03em;
    }

    .deal-text p {
        font-size: 0.7rem;
        margin: 0.0625rem 0 0;
        opacity: 0.8;
        line-height: 1.1;
    }

    .deal-icon,
    .deal-action {
        font-size: 1.125rem;
    }
}

    .overview-section,
    .analytics-section {
        padding: 0.75rem;
    }

    .card {
        border-radius: 0.5rem;
    }

    .card-header,
    .card-content {
        padding: 1rem;
    }

    /* Ultra-compact activity summary for small screens */
    .social-activity-section {
        padding: 0.75rem;
    }

    .social-activity-section h2 {
        font-size: 1.125rem;
        margin-bottom: 0.75rem;
        line-height: 1.1;
    }

    .date-selector select {
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }

    .social-summary-card .card-header {
        padding: 0.75rem 1rem 0.5rem;
    }

    .social-summary-card .card-header h3 {
        font-size: 0.9rem;
        line-height: 1.1;
    }

    .social-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
        padding: 0.75rem 1rem 1rem;
    }

    .stat-value {
        font-size: 1.25rem;
        margin-bottom: 0.125rem;
        line-height: 1.05;
    }

    .stat.highlight .stat-value {
        font-size: 1.375rem;
    }

    .stat-label {
        font-size: 0.65rem;
        line-height: 1.1;
        letter-spacing: 0.03em;
    }

    .card-header {
        padding: 0.75rem 1rem 0.5rem;
    }

    .card-header h3 {
        font-size: 0.9rem;
        line-height: 1.1;
    }

    .card-content,
    .card-stats {
        padding: 0.625rem 0.875rem 0.875rem;
        gap: 0.5rem;
        grid-template-columns: repeat(2, 1fr);
    }

    .stat.highlight .stat-value {
        font-size: 1.25rem;
    }

    .stat-value {
        font-size: 1.125rem;
    }
}