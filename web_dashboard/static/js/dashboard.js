// Cheeks Dashboard JavaScript

// Update last updated time
function updateLastUpdated() {
    const now = new Date();
    document.getElementById('last-updated').textContent = now.toLocaleString();
}

// Load OnlyMonster Overview
async function loadOnlyMonsterOverview() {
    try {
        const response = await fetch('/api/onlymonster/overview');
        const data = await response.json();
        
        if (data.error) {
            console.error('OnlyMonster Overview Error:', data.error);
            return;
        }
        
        document.getElementById('om-period').textContent = data.period;
        document.getElementById('om-fans').textContent = `+${data.total_fans}`;
        document.getElementById('om-clicks').textContent = formatNumber(data.total_clicks);
        document.getElementById('om-earnings').textContent = `$${data.total_earnings.toFixed(2)}`;
        document.getElementById('om-active').textContent = `${data.active_links}/${data.total_links}`;
        
    } catch (error) {
        console.error('Error loading OnlyMonster overview:', error);
    }
}

// Load Social Overview
async function loadSocialOverview() {
    try {
        const response = await fetch('/api/social/overview');
        const data = await response.json();
        
        if (data.error) {
            console.error('Social Overview Error:', data.error);
            return;
        }
        
        document.getElementById('social-period').textContent = 'Last 24 Hours';
        document.getElementById('social-posts').textContent = data.total_posts;
        document.getElementById('social-accounts').textContent = `${data.accounts_with_posts}/${data.total_accounts}`;
        document.getElementById('reddit-removed').textContent = data.reddit_removed_posts;
        
        // OnlyFans deal
        const dealElement = document.getElementById('onlyfans-deal');
        if (data.onlyfans_deal.has_deal) {
            dealElement.classList.add('has-deal');
            dealElement.querySelector('.stat-value').textContent = `${data.onlyfans_deal.deal_percentage}% OFF`;
        } else {
            dealElement.querySelector('.stat-value').textContent = 'No Deal';
        }
        
    } catch (error) {
        console.error('Error loading social overview:', error);
    }
}

// Load OnlyMonster Metrics
async function loadOnlyMonsterMetrics() {
    try {
        const response = await fetch('/api/onlymonster/metrics');
        const data = await response.json();
        
        if (data.error) {
            console.error('OnlyMonster Metrics Error:', data.error);
            return;
        }
        
        const tableElement = document.getElementById('metrics-table');
        
        if (data.metrics.length === 0) {
            tableElement.innerHTML = '<div class="loading">No metrics data available</div>';
            return;
        }
        
        let tableHTML = `
            <div class="metric-row" style="font-weight: 600; border-bottom: 2px solid #ddd;">
                <div>Link Name</div>
                <div style="text-align: center;">Fans</div>
                <div style="text-align: center;">Clicks</div>
                <div style="text-align: center;">Conv. Rate</div>
                <div style="text-align: center;">Performance</div>
            </div>
        `;
        
        data.metrics.forEach(metric => {
            const fanClass = metric.fan_growth > 0 ? 'positive' : (metric.fan_growth < 0 ? 'negative' : 'neutral');
            const clickClass = metric.click_growth > 0 ? 'positive' : (metric.click_growth < 0 ? 'negative' : 'neutral');
            const performanceClass = (metric.fan_deviation > 0 || metric.click_deviation > 0) ? 'positive' : 
                                   (metric.fan_deviation < 0 || metric.click_deviation < 0) ? 'negative' : 'neutral';
            
            tableHTML += `
                <div class="metric-row">
                    <div class="metric-name">${metric.name}</div>
                    <div class="metric-value ${fanClass}">${metric.fan_growth >= 0 ? '+' : ''}${metric.fan_growth}</div>
                    <div class="metric-value ${clickClass}">${metric.click_growth >= 0 ? '+' : ''}${metric.click_growth}</div>
                    <div class="metric-value conversion-rate">${metric.conversion_rate.toFixed(2)}%</div>
                    <div class="metric-value ${performanceClass}">
                        ${metric.fan_deviation > 0.5 ? '📈' : metric.fan_deviation < -0.5 ? '📉' : '➡️'}
                    </div>
                </div>
            `;
        });
        
        tableElement.innerHTML = tableHTML;
        
    } catch (error) {
        console.error('Error loading OnlyMonster metrics:', error);
    }
}


// Load Social Details
async function loadSocialDetails() {
    try {
        const response = await fetch('/api/social/details');
        const data = await response.json();
        
        if (data.error) {
            console.error('Social Details Error:', data.error);
            return;
        }
        
        // TikTok Accounts
        const tiktokElement = document.getElementById('tiktok-accounts');
        let tiktokHTML = '';
        
        data.accounts.tiktok.forEach(account => {
            const statusClass = account.status === 'active' ? 'active' : 'inactive';
            const postTimes = account.post_times.length > 0 ? 
                ` (${account.post_times.slice(0, 3).join(', ')})` : '';
                
            tiktokHTML += `
                <div class="account-item">
                    <div class="account-name">${account.username}</div>
                    <div class="account-stats">
                        <div class="post-count ${statusClass}">${account.posts} posts${postTimes}</div>
                    </div>
                </div>
            `;
        });
        
        tiktokElement.innerHTML = tiktokHTML || '<div class="loading">No TikTok accounts</div>';
        
        // Instagram Accounts
        const instagramElement = document.getElementById('instagram-accounts');
        let instagramHTML = '';
        
        data.accounts.instagram.forEach(account => {
            const statusClass = account.status === 'active' ? 'active' : 
                              account.status === 'error' ? 'partial' : 'inactive';
            const postTimes = account.post_times.length > 0 ? 
                ` (${account.post_times.slice(0, 3).join(', ')})` : '';
            const error = account.error ? ` - ${account.error}` : '';
                
            instagramHTML += `
                <div class="account-item">
                    <div class="account-name">${account.username}${error}</div>
                    <div class="account-stats">
                        <div class="post-count ${statusClass}">${account.posts} posts${postTimes}</div>
                    </div>
                </div>
            `;
        });
        
        instagramElement.innerHTML = instagramHTML || '<div class="loading">No Instagram accounts</div>';
        
        // Reddit Accounts
        const redditElement = document.getElementById('reddit-accounts');
        let redditHTML = '';
        
        data.accounts.reddit.forEach(account => {
            const statusClass = account.status === 'active' ? 'active' : 
                              account.status === 'partial' ? 'partial' : 'inactive';
            const postTimes = account.post_times.length > 0 ? 
                ` (${account.post_times.slice(0, 3).join(', ')})` : '';
            
            let removedText = '';
            if (account.removed_posts > 0) {
                const reasons = Object.entries(account.removal_reasons)
                    .map(([reason, count]) => `${reason}: ${count}`)
                    .join(', ');
                removedText = `<div class="reddit-removed">${account.removed_posts} removed (${reasons})</div>`;
            }
                
            redditHTML += `
                <div class="account-item">
                    <div class="account-name">
                        ${account.username}
                        ${removedText}
                    </div>
                    <div class="account-stats">
                        <div class="post-count ${statusClass}">${account.posts} posts${postTimes}</div>
                    </div>
                </div>
            `;
        });
        
        redditElement.innerHTML = redditHTML || '<div class="loading">No Reddit accounts</div>';
        
    } catch (error) {
        console.error('Error loading social details:', error);
    }
}

// Utility function to format numbers
function formatNumber(num) {
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
}

// Auto-refresh function
function autoRefresh() {
    loadOnlyMonsterOverview();
    loadSocialOverview();
    loadOnlyMonsterMetrics();
    loadSocialDetails();
    updateLastUpdated();
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Initial load
    autoRefresh();
    
    // Auto-refresh every 5 minutes
    setInterval(autoRefresh, 5 * 60 * 1000);
    
    // Update timestamp every minute
    setInterval(updateLastUpdated, 60 * 1000);
});

// Manual refresh functions for buttons
function refreshOnlyMonster() {
    loadOnlyMonsterOverview();
    loadOnlyMonsterMetrics();
}

function refreshSocial() {
    loadSocialOverview();
    loadSocialDetails();
}